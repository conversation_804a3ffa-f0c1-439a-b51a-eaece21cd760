﻿<?xml version="1.0" encoding="utf-8"?>
<root>
  <!--
    Microsoft ResX Schema

    Version 2.0

    The primary goals of this format is to allow a simple XML format
    that is mostly human readable. The generation and parsing of the
    various data types are done through the TypeConverter classes
    associated with the data types.

    Example:

    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>

    There are any number of "resheader" rows that contain simple
    name/value pairs.

    Each data row contains a name, and value. The row also contains a
    type or mimetype. Type corresponds to a .NET class that support
    text/value conversion through the TypeConverter architecture.
    Classes that don't support this are serialized and stored with the
    mimetype set.

    The mimetype is used for serialized objects, and tells the
    ResXResourceReader how to depersist the object. This is currently not
    extensible. For a given mimetype the value must be set accordingly:

    Note - application/x-microsoft.net.object.binary.base64 is the format
    that the ResXResourceWriter will generate, however the reader can
    read any of the formats listed below.

    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <data name="(filtered from _MAX_ total records)" xml:space="preserve">
    <value>(_MAX_ kayıt içerisinden filtrelendi)</value>
    <comment>Filtered records info text</comment>
  </data>
  <data name="0.0.0.0" xml:space="preserve">
    <value>0.0.0.0</value>
    <comment>Default IP address</comment>
  </data>
  <data name="24/7 customer support" xml:space="preserve">
    <value>7/24 müşteri desteği</value>
    <comment>Benefit 4</comment>
  </data>
  <data name="A percentage for this package and level already exists. Please edit the existing one." xml:space="preserve">
    <value>Bu paket ve seviye için bir yüzde zaten mevcut. Lütfen mevcut olanı düzenleyin.</value>
    <comment>Error message for duplicate package reward percentage</comment>
  </data>
  <data name="A record of each reward will be stored in the system." xml:space="preserve">
    <value>Her ödülün kaydı sistemde saklanacaktır.</value>
  </data>
  <data name="A setting with this key already exists" xml:space="preserve">
    <value>Bu anahtara sahip bir ayar zaten mevcut</value>
    <comment>Error message when trying to create a setting with an existing key</comment>
  </data>
  <data name="A wallet already exists for this user and coin" xml:space="preserve">
    <value>Bu kullanıcı ve coin için zaten bir cüzdan mevcut</value>
    <comment>Error message when trying to create a wallet that already exists</comment>
  </data>
  <data name="About RazeWin" xml:space="preserve">
    <value>RazeWin Hakkında</value>
    <comment>About page title</comment>
  </data>
  <data name="About Us" xml:space="preserve">
    <value>Hakkımızda</value>
    <comment>About us menu item</comment>
  </data>
  <data name="Access Denied" xml:space="preserve">
    <value>Erişim Reddedildi</value>
    <comment>Error message when access is denied</comment>
  </data>
  <data name="Account Activity" xml:space="preserve">
    <value>Hesap Aktivitesi</value>
    <comment>Report section title</comment>
  </data>
  <data name="Account closed successfully" xml:space="preserve">
    <value>Hesap başarıyla kapatıldı</value>
    <comment>Success message for emergency close</comment>
  </data>
  <data name="Account Creation Text" xml:space="preserve">
    <value>Platformumuzu kullanabilmek için gerçek kimlik bilgilerinizle kayıt olmanız gerekmektedir. Verdiğiniz tüm bilgilerin doğru ve güncel olmasından siz sorumlusunuz.</value>
    <comment>Text for account creation section in user agreement</comment>
  </data>
  <data name="Account Creation Title" xml:space="preserve">
    <value>Hesap Oluşturma</value>
    <comment>Title for account creation section in user agreement</comment>
  </data>
  <data name="Account Holder" xml:space="preserve">
    <value>Hesap Sahibi</value>
    <comment>Account holder label</comment>
  </data>
  <data name="Account holder name cannot exceed 100 characters" xml:space="preserve">
    <value>Hesap sahibi adı 100 karakteri geçemez</value>
    <comment>Validation message for account holder length</comment>
  </data>
  <data name="Account Holder Name Too Long" xml:space="preserve">
    <value>Hesap sahibi adı 100 karakteri geçemez</value>
    <comment>Validation message for account holder length</comment>
  </data>
  <data name="Account Management" xml:space="preserve">
    <value>Hesap Yönetimi</value>
    <comment>Admin page title for account management</comment>
  </data>
  <data name="Account maturity processed successfully" xml:space="preserve">
    <value>Hesap vadesi başarıyla işlendi</value>
    <comment>Success message for forced maturity</comment>
  </data>
  <data name="Accumulated Interest" xml:space="preserve">
    <value>Birikmiş Faiz</value>
    <comment>Label for accumulated interest in interest history</comment>
  </data>
  <data name="Actions" xml:space="preserve">
    <value>İşlemler</value>
    <comment>Actions column header</comment>
  </data>
  <data name="Active" xml:space="preserve">
    <value>Aktif</value>
    <comment>Active status</comment>
  </data>
  <data name="Active Savings Accounts" xml:space="preserve">
    <value>Aktif Tasarruf Hesapları</value>
    <comment>Number of active savings accounts</comment>
  </data>
  <data name="Additional Notes (Optional)" xml:space="preserve">
    <value>Ek Notlar (İsteğe Bağlı)</value>
    <comment>Label for optional notes field</comment>
  </data>
  <data name="Address" xml:space="preserve">
    <value>Adres</value>
    <comment>Address label</comment>
  </data>
  <data name="Address copied to clipboard" xml:space="preserve">
    <value>Adres panoya kopyalandı</value>
    <comment>Message shown when an address is copied to clipboard</comment>
  </data>
  <data name="Admin" xml:space="preserve">
    <value>Yönetici</value>
    <comment>Admin label</comment>
  </data>
  <data name="Admin Adjustment" xml:space="preserve">
    <value>Admin Düzeltmesi</value>
    <comment>Transaction type for admin adjustments</comment>
  </data>
  <data name="Admin Panel" xml:space="preserve">
    <value>Yönetici Paneli</value>
    <comment>Admin panel link</comment>
  </data>
  <data name="After sending cryptocurrency to one of the addresses above, please fill out this form to report your deposit." xml:space="preserve">
    <value>Yukarıdaki adreslerden birine kripto para gönderdikten sonra, lütfen yatırımınızı bildirmek için bu formu doldurun.</value>
    <comment>Instructions for reporting cryptocurrency deposits</comment>
  </data>
  <data name="Agreement Acceptance Text" xml:space="preserve">
    <value>Bu sözleşmeyi kabul ederek, yukarıdaki tüm şartları okuduğunuzu ve anladığınızı beyan etmiş olursunuz.</value>
    <comment>Text for agreement acceptance in user agreement</comment>
  </data>
  <data name="All Accounts" xml:space="preserve">
    <value>Tüm Hesaplar</value>
    <comment>All accounts option in filter</comment>
  </data>
  <data name="All Payments" xml:space="preserve">
    <value>Tüm Ödemeler</value>
    <comment>Label for all payments section</comment>
  </data>
  <data name="All rights reserved." xml:space="preserve">
    <value>Tüm hakları saklıdır.</value>
    <comment>Copyright notice</comment>
  </data>
  <data name="All Savings Accounts" xml:space="preserve">
    <value>Tüm Tasarruf Hesapları</value>
    <comment>Title for all savings accounts section</comment>
  </data>
  <data name="All Trades" xml:space="preserve">
    <value>Tüm İşlemler</value>
    <comment>Label for all trades in trade history</comment>
  </data>
  <data name="Amount" xml:space="preserve">
    <value>Tutar</value>
    <comment>Amount label</comment>
  </data>
  <data name="Amount (Cents)" xml:space="preserve">
    <value>Tutar (Kuruş)</value>
    <comment>Amount cents label</comment>
  </data>
  <data name="Amount (RZW)" xml:space="preserve">
    <value>Miktar (RZW)</value>
  </data>
  <data name="Amount (TRY)" xml:space="preserve">
    <value>Tutar (TRY)</value>
    <comment>Label for amount in Turkish Lira</comment>
  </data>
  <data name="Amount Cannot Be Negative" xml:space="preserve">
    <value>Tutar negatif olamaz</value>
    <comment>Validation message for negative amount</comment>
  </data>
  <data name="Amount cannot exceed 1,000,000" xml:space="preserve">
    <value>Tutar 1.000.000'u geçemez</value>
    <comment>Validation message for maximum amount</comment>
  </data>
  <data name="Amount must be at least 0.00000001" xml:space="preserve">
    <value>Tutar en az 0,00000001 olmalıdır</value>
    <comment>Validation message for minimum amount</comment>
  </data>
  <data name="Amount must be between" xml:space="preserve">
    <value>Miktar şu aralıkta olmalı</value>
    <comment>Validation message for amount limits</comment>
  </data>
  <data name="Amount must be between {0} and {1} RZW" xml:space="preserve">
    <value>Miktar {0} ile {1} RZW arasında olmalıdır</value>
    <comment>Validation message with plan limits</comment>
  </data>
  <data name="Amount Required" xml:space="preserve">
    <value>Tutar gereklidir</value>
    <comment>Amount required validation message</comment>
  </data>
  <data name="Amount to buy" xml:space="preserve">
    <value>Al─▒nacak miktar</value>
    <comment>Label for buy amount input</comment>
  </data>
  <data name="Amount to Receive" xml:space="preserve">
    <value>Alınacak Miktar</value>
    <comment>Amount to receive in trading form</comment>
  </data>
  <data name="Amount to sell" xml:space="preserve">
    <value>Satılacak Miktar</value>
    <comment>Amount to sell in trading form</comment>
  </data>
  <data name="Amount to spend" xml:space="preserve">
    <value>Harcanacak Miktar</value>
    <comment>Amount to spend in trading form</comment>
  </data>
  <data name="Amount:" xml:space="preserve">
    <value>Tutar:</value>
    <comment>Amount label</comment>
  </data>
  <data name="An error occurred during early withdrawal. Please try again." xml:space="preserve">
    <value>Erken çekim sırasında bir hata oluştu. Lütfen tekrar deneyin.</value>
    <comment>Error message for early withdrawal failure</comment>
  </data>
  <data name="An error occurred while claiming interest. Please try again." xml:space="preserve">
    <value>Faiz alınırken bir hata oluştu. Lütfen tekrar deneyin.</value>
    <comment>General error message for claim interest action.</comment>
  </data>
  <data name="An error occurred while closing the account" xml:space="preserve">
    <value>Hesap kapatılırken bir hata oluştu</value>
    <comment>Error message for account closure</comment>
  </data>
  <data name="An error occurred while creating the plan" xml:space="preserve">
    <value>Plan oluşturulurken bir hata oluştu</value>
    <comment>Error message for plan creation</comment>
  </data>
  <data name="An error occurred while creating the savings account. Please try again." xml:space="preserve">
    <value>Tasarruf hesabı oluşturulurken bir hata oluştu. Lütfen tekrar deneyin.</value>
    <comment>Error message for account creation failure</comment>
  </data>
  <data name="An error occurred while deleting the plan" xml:space="preserve">
    <value>Plan silinirken bir hata oluştu</value>
    <comment>Error message for plan deletion</comment>
  </data>
  <data name="An error occurred while distributing rewards: {0}" xml:space="preserve">
    <value>Ödüller dağıtılırken bir hata oluştu: {0}</value>
  </data>
  <data name="An error occurred while processing maturity" xml:space="preserve">
    <value>Vade işlenirken bir hata oluştu</value>
    <comment>Error message for maturity processing</comment>
  </data>
  <data name="An error occurred while sending the verification email. Please try again later." xml:space="preserve">
    <value>Doğrulama e-postası gönderilirken bir hata oluştu. Lütfen daha sonra tekrar deneyin.</value>
    <comment>Generic error message for email sending</comment>
  </data>
  <data name="An error occurred while updating the plan" xml:space="preserve">
    <value>Plan güncellenirken bir hata oluştu</value>
    <comment>Error message for plan update</comment>
  </data>
  <data name="An error occurred: {0}" xml:space="preserve">
    <value>Bir hata oluştu: {0}</value>
    <comment>Generic error message with parameter</comment>
  </data>
  <data name="and" xml:space="preserve">
    <value>ve</value>
    <comment>Conjunction word for amount limits</comment>
  </data>
  <data name="and their referrals." xml:space="preserve">
    <value>ve onların getirdiği kullanıcıları.</value>
    <comment>Used in referral tree page</comment>
  </data>
  <data name="Announcement" xml:space="preserve">
    <value>Duyuru</value>
    <comment>Single announcement title</comment>
  </data>
  <data name="Announcement Content" xml:space="preserve">
    <value>Duyuru İçeriği</value>
    <comment>Announcement content field label</comment>
  </data>
  <data name="Announcement Date" xml:space="preserve">
    <value>Duyuru Tarihi</value>
    <comment>Announcement date field label</comment>
  </data>
  <data name="Announcement List" xml:space="preserve">
    <value>Duyuru Listesi</value>
    <comment>Announcement list page title</comment>
  </data>
  <data name="Announcement Status" xml:space="preserve">
    <value>Duyuru Durumu</value>
    <comment>Announcement status field label</comment>
  </data>
  <data name="Announcement Successfully Created" xml:space="preserve">
    <value>Duyuru başarıyla oluşturuldu</value>
    <comment>Success message for announcement creation</comment>
  </data>
  <data name="Announcement Successfully Deleted" xml:space="preserve">
    <value>Duyuru başarıyla silindi</value>
    <comment>Success message for announcement deletion</comment>
  </data>
  <data name="Announcement Successfully Updated" xml:space="preserve">
    <value>Duyuru başarıyla güncellendi</value>
    <comment>Success message for announcement update</comment>
  </data>
  <data name="Announcement Title" xml:space="preserve">
    <value>Duyuru Başlığı</value>
    <comment>Announcement title field label</comment>
  </data>
  <data name="Announcements" xml:space="preserve">
    <value>Duyurular</value>
    <comment>Main announcements section title</comment>
  </data>
  <data name="Annual Percentage Yield" xml:space="preserve">
    <value>Yıllık Getiri Oranı</value>
    <comment>APY'nin tam açıklaması, her iki dilde de İngilizce gösterilecek</comment>
  </data>
  <data name="Anti Money Laundering Policy" xml:space="preserve">
    <value>Kara Para Aklamayla Mücadele Politikası</value>
    <comment>Title for Anti Money Laundering Policy page</comment>
  </data>
  <data name="Any additional information about your deposit" xml:space="preserve">
    <value>Yatırımınız hakkında ek bilgiler</value>
    <comment>Label for additional deposit information field</comment>
  </data>
  <data name="API" xml:space="preserve">
    <value>API</value>
    <comment>API column header</comment>
  </data>
  <data name="API Pair" xml:space="preserve">
    <value>API Çifti</value>
    <comment>Label for API pair selection</comment>
  </data>
  <data name="API Service" xml:space="preserve">
    <value>API Servisi</value>
    <comment>API service column header</comment>
  </data>
  <data name="Apply" xml:space="preserve">
    <value>Başvur</value>
    <comment>Apply button</comment>
  </data>
  <data name="Approved" xml:space="preserve">
    <value>Onaylandı</value>
    <comment>Approved status</comment>
  </data>
  <data name="Approved Deposits" xml:space="preserve">
    <value>Onaylanmış Para Yatırma İşlemleri</value>
    <comment>Title for approved deposits list</comment>
  </data>
  <data name="Approved or rejected payments cannot be edited." xml:space="preserve">
    <value>Onaylanmış veya reddedilmiş ödemeler düzenlenemez.</value>
    <comment>Error message when trying to edit approved/rejected payments</comment>
  </data>
  <data name="Approved or rejected withdrawals cannot be edited." xml:space="preserve">
    <value>Onaylanmış veya reddedilmiş para çekme işlemleri düzenlenemez.</value>
    <comment>Error message when trying to edit approved/rejected withdrawals</comment>
  </data>
  <data name="APY" xml:space="preserve">
    <value>YGO</value>
    <comment>Annual Percentage Yield - Yıllık Getiri Oranı</comment>
  </data>
  <data name="Are You Sure Delete Announcement" xml:space="preserve">
    <value>Bu duyuruyu silmek istediğinizden emin misiniz?</value>
    <comment>Confirmation message for announcement deletion</comment>
  </data>
  <data name="Are you sure you want to" xml:space="preserve">
    <value>Emin misiniz</value>
    <comment>Start of confirmation message</comment>
  </data>
  <data name="Are you sure you want to change the auto-renew setting?" xml:space="preserve">
    <value>Otomatik yenileme ayarını değiştirmek istediğinize emin misiniz?</value>
    <comment>SweetAlert2 otomatik yenileme onay metni</comment>
  </data>
  <data name="Are you sure you want to delete this user?" xml:space="preserve">
    <value>Bu kullanıcıyı silmek istediğinizden emin misiniz?</value>
    <comment>Confirmation message for user deletion</comment>
  </data>
  <data name="Are you sure you want to emergency close this account?" xml:space="preserve">
    <value>Bu hesabı acil olarak kapatmak istediğinizden emin misiniz?</value>
    <comment>Confirmation message for emergency close</comment>
  </data>
  <data name="Are you sure you want to force maturity for this account?" xml:space="preserve">
    <value>Bu hesabın vadesini zorla doldurmak istediğinizden emin misiniz?</value>
    <comment>Confirmation message for forced maturity</comment>
  </data>
  <data name="Are you sure you want to purchase the" xml:space="preserve">
    <value>Satın almak istediğinizden emin misiniz</value>
    <comment>Confirmation message part 1</comment>
  </data>
  <data name="Are you sure you want to withdraw early? This action cannot be undone." xml:space="preserve">
    <value>Erken çekmek istediğinizden emin misiniz? Bu işlem geri alınamaz.</value>
    <comment>Early withdrawal confirmation message</comment>
  </data>
  <data name="Are you sure?" xml:space="preserve">
    <value>Emin misiniz?</value>
    <comment>SweetAlert2 onay başlığı</comment>
  </data>
  <data name="Attract your customers' attention with vibrant and eye-catching visuals." xml:space="preserve">
    <value>Canlı ve göz alıcı görsellerle müşterilerinizin dikkatini çekin.</value>
    <comment>Increased visibility description</comment>
  </data>
  <data name="Auto Generated Description For Tracking" xml:space="preserve">
    <value>Bu açıklama otomatik olarak oluşturulmuştur ve para yatırma işleminizin takibi için kullanılacaktır.</value>
    <comment>Description field help text</comment>
  </data>
  <data name="Auto Refresh" xml:space="preserve">
    <value>Otomatik Yenile</value>
    <comment>Label for auto refresh toggle</comment>
  </data>
  <data name="Auto Renew" xml:space="preserve">
    <value>Otomatik Yenileme</value>
    <comment>Label for auto renew checkbox</comment>
  </data>
  <data name="Automatically renew when maturity date is reached" xml:space="preserve">
    <value>Vade tarihi geldiğinde otomatik olarak yenile</value>
    <comment>Description for auto renew option</comment>
  </data>
  <data name="Auto-renew setting updated successfully." xml:space="preserve">
    <value>Otomatik yenileme ayarı başarıyla güncellendi.</value>
    <comment>Otomatik yenileme güncelleme başarı mesajı</comment>
  </data>
  <data name="Available Balance" xml:space="preserve">
    <value>Kullanılabilir Bakiye</value>
    <comment>Available balance label in trading form</comment>
  </data>
  <data name="Available for new savings accounts" xml:space="preserve">
    <value>Yeni tasarruf hesapları için kullanılabilir</value>
    <comment>Description for available balance</comment>
  </data>
  <data name="Available Packages" xml:space="preserve">
    <value>Mevcut Paketler</value>
    <comment>Section heading for available packages</comment>
  </data>
  <data name="Available Savings Plans" xml:space="preserve">
    <value>Mevcut Tasarruf Hesabı Planları</value>
    <comment>Available savings plans section title</comment>
  </data>
  <data name="Available Withdrawal Limit" xml:space="preserve">
    <value>Çekilebilir Bakiye</value>
    <comment>Available withdrawal limit label</comment>
  </data>
  <data name="Average Daily Interest" xml:space="preserve">
    <value>Ortalama Günlük Faiz</value>
    <comment>Label for average daily interest amount</comment>
  </data>
  <data name="Average Payment Amount" xml:space="preserve">
    <value>Ortalama Ödeme Tutarı</value>
    <comment>Label for average payment amount</comment>
  </data>
  <data name="Average Per Payment" xml:space="preserve">
    <value>Ödeme Başına Ortalama</value>
    <comment>Average amount per payment</comment>
  </data>
  <data name="Average Reward Amount" xml:space="preserve">
    <value>Ortalama Ödül Tutarı</value>
    <comment>Label for average reward amount</comment>
  </data>
  <data name="Back to List" xml:space="preserve">
    <value>Listeye Dön</value>
    <comment>Label for back to list button</comment>
  </data>
  <data name="Back to My Savings" xml:space="preserve">
    <value>Tasarruf Hesaplarıma Dön</value>
    <comment>Button to go back to savings list</comment>
  </data>
  <data name="Background Service Status" xml:space="preserve">
    <value>Arka Plan Servis Durumu</value>
    <comment>Label for background service status</comment>
  </data>
  <data name="Background Services Status" xml:space="preserve">
    <value>Arka Plan Servisleri Durumu</value>
    <comment>Title for background services status section</comment>
  </data>
  <data name="Balance" xml:space="preserve">
    <value>Bakiye</value>
  </data>
  <data name="Balance After Interest" xml:space="preserve">
    <value>Faiz Sonrası Bakiye</value>
    <comment>Balance after interest column header</comment>
  </data>
  <data name="Balance History" xml:space="preserve">
    <value>Bakiye Geçmişi</value>
    <comment>Page title for balance history</comment>
  </data>
  <data name="Balance Sufficiency Check" xml:space="preserve">
    <value>Bakiye Yeterlilik Kontrolü</value>
    <comment>Check item for balance sufficiency</comment>
  </data>
  <data name="Balance Transactions" xml:space="preserve">
    <value>Bakiye İşlemleri</value>
    <comment>Title for the Balance Transactions list</comment>
  </data>
  <data name="Bank Account" xml:space="preserve">
    <value>Banka Hesabı</value>
    <comment>Label for bank account</comment>
  </data>
  <data name="Bank Information" xml:space="preserve">
    <value>Banka Bilgileri</value>
    <comment>Bank information section header</comment>
  </data>
  <data name="Bank Name" xml:space="preserve">
    <value>Banka Adı</value>
    <comment>Label for bank name</comment>
  </data>
  <data name="Bank Transfer" xml:space="preserve">
    <value>Banka Transferi</value>
    <comment>Bank transfer deposit method</comment>
  </data>
  <data name="Bank:" xml:space="preserve">
    <value>Banka:</value>
    <comment>Bank label</comment>
  </data>
  <data name="Bank: {0}, IBAN: {1}, Transfer Time: {2}, Customer Note: {3}" xml:space="preserve">
    <value>Banka: {0}, IBAN: {1}, Transfer Saati: {2}, Müşteri Notu: {3}</value>
    <comment>Bank transfer details format</comment>
  </data>
  <data name="Bank: {0}, Reference: {1}" xml:space="preserve">
    <value>Banka: {0}, Referans: {1}</value>
    <comment>Bank reference format</comment>
  </data>
  <data name="Banks" xml:space="preserve">
    <value>Bankalar</value>
    <comment>Banks menu item</comment>
  </data>
  <data name="Be part of the RazeWin community and experience the future of cryptocurrency trading." xml:space="preserve">
    <value>RazeWin topluluğunun bir parçası olun ve kripto para ticaretinin geleceğini deneyimleyin.</value>
    <comment>Community section description</comment>
  </data>
  <data name="Benefits" xml:space="preserve">
    <value>Faydalar</value>
    <comment>Label for benefits</comment>
  </data>
  <data name="Birth Date" xml:space="preserve">
    <value>Doğum Tarihi</value>
    <comment>Birth date field label</comment>
  </data>
  <data name="Bitcoin (BTC) Deposit Address" xml:space="preserve">
    <value>Bitcoin (BTC) Yatırma Adresi</value>
    <comment>Label for Bitcoin deposit address</comment>
  </data>
  <data name="Blockchain Powered Trading Experience" xml:space="preserve">
    <value>Blokzincir destekli alım-satım deneyimi ile kazancınızı geleceğe taşıyın</value>
    <comment>Description for the second slider</comment>
  </data>
  <data name="BNB (BEP20) Deposit Address" xml:space="preserve">
    <value>BNB (BEP20) Yatırma Adresi</value>
    <comment>Label for BNB deposit address</comment>
  </data>
  <data name="BNB Address" xml:space="preserve">
    <value>BNB Adresi</value>
    <comment>Label for BNB address</comment>
  </data>
  <data name="Bonus" xml:space="preserve">
    <value>Bonus</value>
    <comment>Bonus label for package purchase</comment>
  </data>
  <data name="Browse Markets" xml:space="preserve">
    <value>Piyasalara Göz At</value>
    <comment>Label for browse markets button</comment>
  </data>
  <data name="BTC Address" xml:space="preserve">
    <value>BTC Adresi</value>
    <comment>Label for BTC address</comment>
  </data>
  <data name="Button not found for element" xml:space="preserve">
    <value>Element için düğme bulunamadı</value>
    <comment>Error message when button for copying is not found</comment>
  </data>
  <data name="Buy" xml:space="preserve">
    <value>Satın Al</value>
    <comment>Buy button text</comment>
  </data>
  <data name="Buy Coin" xml:space="preserve">
    <value>Coin Satın Al</value>
    <comment>Label for buy coin button</comment>
  </data>
  <data name="Buy Price" xml:space="preserve">
    <value>Alış Fiyatı</value>
    <comment>Buy price column header</comment>
  </data>
  <data name="Call Me Back" xml:space="preserve">
    <value>Beni Geri Arayın</value>
    <comment>Call me back link text</comment>
  </data>
  <data name="Cancel" xml:space="preserve">
    <value>Vazgeç</value>
    <comment>Cancel button text</comment>
  </data>
  <data name="Cancelled" xml:space="preserve">
    <value>İptal Edildi</value>
    <comment>Label for cancelled status</comment>
  </data>
  <data name="Cannot delete package because it has reward percentages. Please remove reward percentages first." xml:space="preserve">
    <value>Paket ödül yüzdeleri olduğu için silinemez. Lütfen önce ödül yüzdelerini kaldırın.</value>
    <comment>Error message when trying to delete a package that has reward percentages</comment>
  </data>
  <data name="Cannot delete package because it is assigned to users. Please remove user assignments first." xml:space="preserve">
    <value>Paket kullanıcılara atandığı için silinemez. Lütfen önce kullanıcı atamalarını kaldırın.</value>
    <comment>Error message when trying to delete a package that is assigned to users</comment>
  </data>
  <data name="Cannot delete plan with active accounts. Please wait for all accounts to mature or be withdrawn." xml:space="preserve">
    <value>Aktif hesapları olan plan silinemez. Lütfen tüm hesapların vadesi dolana veya çekilene kadar bekleyin.</value>
    <comment>Error message when trying to delete plan with active accounts</comment>
  </data>
  <data name="Cannot delete user package because it has associated referral rewards. Please remove referral rewards first." xml:space="preserve">
    <value>Kullanıcı paketi ilişkili referans ödülleri olduğu için silinemez. Lütfen önce referans ödüllerini kaldırın.</value>
    <comment>Error message when trying to delete a user package that has associated referral rewards</comment>
  </data>
  <data name="Cannot determine when the deposit was approved" xml:space="preserve">
    <value>Para yatırma işleminin ne zaman onaylandığı belirlenemiyor</value>
    <comment>Error message for deposit approval date</comment>
  </data>
  <data name="Cannot Reverse" xml:space="preserve">
    <value>Geri Alınamaz</value>
    <comment>Button text for deposits that cannot be reversed</comment>
  </data>
  <data name="Cannot reverse deposit because rewards have been distributed" xml:space="preserve">
    <value>Ödüller dağıtıldığı için para yatırma işlemi geri alınamaz</value>
    <comment>Error message for reward distribution</comment>
  </data>
  <data name="Cannot reverse deposit because user has made trades after this deposit was approved" xml:space="preserve">
    <value>Kullanıcı bu para yatırma işlemi onaylandıktan sonra ticaret yaptığı için geri alınamaz</value>
    <comment>Error message for trade activity</comment>
  </data>
  <data name="Cannot reverse deposit because user has made withdrawals after this deposit was approved" xml:space="preserve">
    <value>Kullanıcı bu para yatırma işlemi onaylandıktan sonra para çekme işlemi yaptığı için geri alınamaz</value>
    <comment>Error message for withdrawal activity</comment>
  </data>
  <data name="Cannot reverse deposit because user has purchased packages after this deposit was approved" xml:space="preserve">
    <value>Kullanıcı bu para yatırma işlemi onaylandıktan sonra paket satın aldığı için geri alınamaz</value>
    <comment>Error message for package purchase</comment>
  </data>
  <data name="Cannot reverse deposit because user's balance is insufficient" xml:space="preserve">
    <value>Kullanıcının bakiyesi yetersiz olduğu için para yatırma işlemi geri alınamaz</value>
    <comment>Error message for balance sufficiency</comment>
  </data>
  <data name="Change 24h" xml:space="preserve">
    <value>24 Saat Değişim</value>
    <comment>24-hour change column header</comment>
  </data>
  <data name="Change Password" xml:space="preserve">
    <value>Şifre Değiştir</value>
    <comment>Change password tab title</comment>
  </data>
  <data name="Changes Text" xml:space="preserve">
    <value>Bu gizlilik politikası zaman zaman güncellenebilir. Önemli değişiklikler size bildirilecektir.</value>
    <comment>Text for changes section in privacy agreement</comment>
  </data>
  <data name="Changes Title" xml:space="preserve">
    <value>Değişiklikler</value>
    <comment>Title for changes section in privacy agreement</comment>
  </data>
  <data name="Chart" xml:space="preserve">
    <value>Grafik</value>
    <comment>Chart page title</comment>
  </data>
  <data name="Choose Your Referral Package" xml:space="preserve">
    <value>Referans Paketinizi Seçin</value>
    <comment>Package selection page heading</comment>
  </data>
  <data name="Claim Interest" xml:space="preserve">
    <value>Faizi Al</value>
    <comment>Button text for claiming matured savings interest.</comment>
  </data>
  <data name="Clear" xml:space="preserve">
    <value>Temizle</value>
    <comment>Clear filters button text</comment>
  </data>
  <data name="Coin" xml:space="preserve">
    <value>Coin</value>
    <comment>Coin label</comment>
  </data>
  <data name="Coin Symbol" xml:space="preserve">
    <value>Coin Sembolü</value>
    <comment>Label for coin symbol</comment>
  </data>
  <data name="Comments" xml:space="preserve">
    <value>Yorumlar</value>
    <comment>Comments section title</comment>
  </data>
  <data name="Company" xml:space="preserve">
    <value>Şirket</value>
    <comment>Label for company</comment>
  </data>
  <data name="Competitive interest rates" xml:space="preserve">
    <value>Rekabetçi faiz oranları</value>
    <comment>Benefit 1</comment>
  </data>
  <data name="Completed" xml:space="preserve">
    <value>Tamamlandı</value>
    <comment>Completed status</comment>
  </data>
  <data name="Confirm Password" xml:space="preserve">
    <value>Şifre Onayı</value>
    <comment>Confirm password field label</comment>
  </data>
  <data name="Confirm Purchase" xml:space="preserve">
    <value>Satın Almayı Onayla</value>
    <comment>Button text to confirm purchase</comment>
  </data>
  <data name="Confirm Purchase Package Message" xml:space="preserve">
    <value>{0} paketini {1} karşılığında satın almak istediğinizden emin misiniz?</value>
    <comment>Confirmation message for package purchase with package name and price</comment>
  </data>
  <data name="Confirm Upgrade" xml:space="preserve">
    <value>Yükseltmeyi Onayla</value>
    <comment>Modal title for package upgrade confirmation</comment>
  </data>
  <data name="Confirm Upgrade Package Message" xml:space="preserve">
    <value>{0} paketine {1} karşılığında yükseltmek istediğinizden emin misiniz?</value>
    <comment>Confirmation message for package upgrade with package name and price</comment>
  </data>
  <data name="Contact" xml:space="preserve">
    <value>İletişim</value>
    <comment>Contact link text</comment>
  </data>
  <data name="Contact Us" xml:space="preserve">
    <value>Bize Ulaşın</value>
    <comment>Contact tab title</comment>
  </data>
  <data name="Cookies And Tracking Text" xml:space="preserve">
    <value>Web sitemiz, kullanıcı deneyimini iyileştirmek için çerezler kullanmaktadır. Tarayıcı ayarlarınızdan çerez kullanımını kontrol edebilirsiniz.</value>
    <comment>Text for cookies and tracking section in privacy agreement</comment>
  </data>
  <data name="Cookies And Tracking Title" xml:space="preserve">
    <value>Çerezler ve İzleme</value>
    <comment>Title for cookies and tracking section in privacy agreement</comment>
  </data>
  <data name="Copied" xml:space="preserve">
    <value>Kopyalandı!</value>
    <comment>Copied notification text</comment>
  </data>
  <data name="Copied!" xml:space="preserve">
    <value>Kopyalandı!</value>
    <comment>Message shown when something is copied to clipboard</comment>
  </data>
  <data name="Copy" xml:space="preserve">
    <value>Kopyala</value>
    <comment>Copy button text</comment>
  </data>
  <data name="Could not find the balance transaction record for this deposit" xml:space="preserve">
    <value>Bu para yatırma işlemi için bakiye işlem kaydı bulunamadı</value>
    <comment>Error message for missing balance transaction</comment>
  </data>
  <data name="Could not parse cryptocurrency data" xml:space="preserve">
    <value>Kripto para verisi ayrıştırılamadı</value>
    <comment>Error message when cryptocurrency data cannot be parsed</comment>
  </data>
  <data name="Count" xml:space="preserve">
    <value>Sayı</value>
    <comment>Count label</comment>
  </data>
  <data name="Countries" xml:space="preserve">
    <value>Ülke</value>
    <comment>Countries label</comment>
  </data>
  <data name="Create" xml:space="preserve">
    <value>Oluştur</value>
    <comment>Label for create button</comment>
  </data>
  <data name="Create a New" xml:space="preserve">
    <value>Yeni Oluştur</value>
    <comment>Create a new button</comment>
  </data>
  <data name="Create a New Market" xml:space="preserve">
    <value>Yeni Piyasa Oluştur</value>
    <comment>Title for create market page</comment>
  </data>
  <data name="Create a New Payment" xml:space="preserve">
    <value>Yeni Ödeme Oluştur</value>
    <comment>Title for create payment page</comment>
  </data>
  <data name="Create a New Setting" xml:space="preserve">
    <value>Yeni Ayar Oluştur</value>
    <comment>Title for create setting page</comment>
  </data>
  <data name="Create a New Trade" xml:space="preserve">
    <value>Yeni İşlem Oluştur</value>
    <comment>Title for create trade page</comment>
  </data>
  <data name="Create a New Wallet" xml:space="preserve">
    <value>Yeni Cüzdan Oluştur</value>
    <comment>Title for create wallet page</comment>
  </data>
  <data name="Create a New Withdrawal" xml:space="preserve">
    <value>Yeni Para Çekme İşlemi Oluştur</value>
    <comment>Title for create withdrawal page</comment>
  </data>
  <data name="Create Account" xml:space="preserve">
    <value>Hesap Oluştur</value>
    <comment>Create account button text</comment>
  </data>
  <data name="Create Account to Start Earning" xml:space="preserve">
    <value>Kazanmaya Başlamak İçin Hesap Oluşturun</value>
    <comment>Create account button</comment>
  </data>
  <data name="Create New Savings Account" xml:space="preserve">
    <value>Yeni Tasarruf Hesabı Aç</value>
    <comment>Button to create new savings account</comment>
  </data>
  <data name="Create Plan" xml:space="preserve">
    <value>Plan Oluştur</value>
    <comment>Button text for creating new savings plan</comment>
  </data>
  <data name="Create Savings Account" xml:space="preserve">
    <value>Tasarruf Hesabı Oluştur</value>
    <comment>Button text for creating savings account</comment>
  </data>
  <data name="Create Your Account" xml:space="preserve">
    <value>Hesabınızı Oluşturun!</value>
    <comment>Create account step title</comment>
  </data>
  <data name="Created by admin" xml:space="preserve">
    <value>Yönetici tarafından oluşturuldu</value>
    <comment>Created by admin text</comment>
  </data>
  <data name="Created Date" xml:space="preserve">
    <value>Oluşturulma Tarihi</value>
    <comment>Label for created date</comment>
  </data>
  <data name="Creation Date" xml:space="preserve">
    <value>Oluşturma Tarihi</value>
    <comment>Creation date label</comment>
  </data>
  <data name="Credit Card" xml:space="preserve">
    <value>Kredi Kartı</value>
    <comment>Credit card payment method</comment>
  </data>
  <data name="Cross Rates" xml:space="preserve">
    <value>Çapraz Oranlar</value>
    <comment>Cross Rates page title</comment>
  </data>
  <data name="Cryptocurrencies" xml:space="preserve">
    <value>Kripto Para</value>
    <comment>Cryptocurrencies label</comment>
  </data>
  <data name="Cryptocurrency Deposit" xml:space="preserve">
    <value>Kripto Para Yatırma</value>
    <comment>Label for cryptocurrency deposit option</comment>
  </data>
  <data name="Cryptocurrency Deposit Methods" xml:space="preserve">
    <value>Kripto Para Yatırma Yöntemleri</value>
    <comment>Header for cryptocurrency deposit methods section</comment>
  </data>
  <data name="Cryptocurrency Deposits" xml:space="preserve">
    <value>Kripto Para Yatırmaları</value>
    <comment>Label for cryptocurrency deposits section</comment>
  </data>
  <data name="Cryptocurrency Information" xml:space="preserve">
    <value>Kripto Para Bilgisi</value>
    <comment>Label for cryptocurrency information section</comment>
  </data>
  <data name="Cryptocurrency Type" xml:space="preserve">
    <value>Kripto Para Türü</value>
    <comment>Label for cryptocurrency type</comment>
  </data>
  <data name="Currency" xml:space="preserve">
    <value>Para Birimi</value>
    <comment>Label for currency</comment>
  </data>
  <data name="Currency_Symbol" xml:space="preserve">
    <value>TL</value>
    <comment>Currency symbol</comment>
  </data>
  <data name="Current" xml:space="preserve">
    <value>Mevcut</value>
    <comment>Label for current package</comment>
  </data>
  <data name="Current Balance" xml:space="preserve">
    <value>Mevcut Bakiye</value>
    <comment>Current balance label</comment>
  </data>
  <data name="Current Culture" xml:space="preserve">
    <value>Mevcut Kültür</value>
    <comment>Label for current culture</comment>
  </data>
  <data name="Current Dealers" xml:space="preserve">
    <value>Mevcut Bayiler</value>
    <comment>Current dealers section title</comment>
  </data>
  <data name="Current Earned Interest" xml:space="preserve">
    <value>Şu Ana Kadar Kazanılan Faiz</value>
    <comment>Current earned interest amount based on elapsed days</comment>
  </data>
  <data name="Current Invites" xml:space="preserve">
    <value>Mevcut Davetler</value>
    <comment>Label for current number of invites</comment>
  </data>
  <data name="Current Package" xml:space="preserve">
    <value>Mevcut Paket</value>
    <comment>Button text for current package</comment>
  </data>
  <data name="Current Password" xml:space="preserve">
    <value>Mevcut Şifre</value>
    <comment>Current password field label</comment>
  </data>
  <data name="Current password is incorrect" xml:space="preserve">
    <value>Mevcut şifre yanlış</value>
    <comment>Error message for incorrect current password</comment>
  </data>
  <data name="Current status" xml:space="preserve">
    <value>Mevcut durum</value>
    <comment>Current status label</comment>
  </data>
  <data name="Current UI Culture" xml:space="preserve">
    <value>Mevcut UI Kültürü</value>
    <comment>Label for current UI culture</comment>
  </data>
  <data name="Customer Login" xml:space="preserve">
    <value>Müşteri Girişi</value>
    <comment>Customer login title on login page</comment>
  </data>
  <data name="Daily" xml:space="preserve">
    <value>Günlük</value>
    <comment>Daily period label</comment>
  </data>
  <data name="Daily Distribution Summary" xml:space="preserve">
    <value>Günlük Dağıtım Özeti</value>
    <comment>Title for daily distribution summary table</comment>
  </data>
  <data name="Daily Interest" xml:space="preserve">
    <value>Günlük Faiz</value>
    <comment>Label for daily interest amount</comment>
  </data>
  <data name="Daily Interest Amount" xml:space="preserve">
    <value>Günlük Faiz Miktarı</value>
    <comment>Label for daily interest amount</comment>
  </data>
  <data name="Daily Interest Paid" xml:space="preserve">
    <value>Günlük Ödenen Faiz</value>
    <comment>Label for daily interest payments</comment>
  </data>
  <data name="Daily Interest Rate" xml:space="preserve">
    <value>Günlük Faiz Oranı</value>
    <comment>Label for daily interest rate</comment>
  </data>
  <data name="Daily Rate" xml:space="preserve">
    <value>Günlük Oran</value>
    <comment>Label for daily interest rate</comment>
  </data>
  <data name="Dashboard" xml:space="preserve">
    <value>Kontrol Paneli</value>
    <comment>Dashboard menu item</comment>
  </data>
  <data name="Data Collection Text" xml:space="preserve">
    <value>RAZEWIN, hizmetlerini sunabilmek için kimlik bilgileri, iletişim bilgileri, finansal bilgiler ve işlem verileri gibi kişisel bilgilerinizi toplamaktadır.</value>
    <comment>Text for data collection section in privacy agreement</comment>
  </data>
  <data name="Data Collection Title" xml:space="preserve">
    <value>Veri Toplama</value>
    <comment>Title for data collection section in privacy agreement</comment>
  </data>
  <data name="Data Issue Detected" xml:space="preserve">
    <value>Veri Sorunu Tespit Edildi</value>
    <comment>Warning message for data integrity issues</comment>
  </data>
  <data name="Data Retention Text" xml:space="preserve">
    <value>Kişisel verileriniz, hesabınız aktif olduğu sürece ve yasal yükümlülüklerimizi yerine getirmek için gerekli olan süre boyunca saklanacaktır.</value>
    <comment>Text for data retention section in privacy agreement</comment>
  </data>
  <data name="Data Retention Title" xml:space="preserve">
    <value>Veri Saklama</value>
    <comment>Title for data retention section in privacy agreement</comment>
  </data>
  <data name="Data Security Text" xml:space="preserve">
    <value>RAZEWIN, kişisel verilerinizi korumak için endüstri standardı güvenlik önlemleri uygulamaktadır. Verileriniz şifreleme teknolojileri ile korunmaktadır.</value>
    <comment>Text for data security section in privacy agreement</comment>
  </data>
  <data name="Data Security Title" xml:space="preserve">
    <value>Veri Güvenliği</value>
    <comment>Title for data security section in privacy agreement</comment>
  </data>
  <data name="Data Sharing Text" xml:space="preserve">
    <value>Kişisel bilgileriniz, açık izniniz olmadan üçüncü taraflarla paylaşılmaz. Ancak, yasal zorunluluk halinde yetkili kurumlarla paylaşılabilir.</value>
    <comment>Text for data sharing section in privacy agreement</comment>
  </data>
  <data name="Data Sharing Title" xml:space="preserve">
    <value>Veri Paylaşımı</value>
    <comment>Title for data sharing section in privacy agreement</comment>
  </data>
  <data name="Data Usage Text" xml:space="preserve">
    <value>Topladığımız bilgiler, hesabınızı yönetmek, işlemlerinizi gerçekleştirmek, yasal yükümlülükleri yerine getirmek ve hizmetlerimizi iyileştirmek için kullanılmaktadır.</value>
    <comment>Text for data usage section in privacy agreement</comment>
  </data>
  <data name="Data Usage Title" xml:space="preserve">
    <value>Veri Kullanımı</value>
    <comment>Title for data usage section in privacy agreement</comment>
  </data>
  <data name="Date" xml:space="preserve">
    <value>Tarih</value>
    <comment>Date column header</comment>
  </data>
  <data name="Date and Time" xml:space="preserve">
    <value>Tarih ve Saat</value>
    <comment>Label for date and time</comment>
  </data>
  <data name="Date Created" xml:space="preserve">
    <value>Oluşturulma Tarihi</value>
    <comment>Date created column</comment>
  </data>
  <data name="DATE WILL BE ANNOUNCED" xml:space="preserve">
    <value>TARİHİ 25 HAZİRANDA</value>
    <comment>Date announcement text for RZW Token slider</comment>
  </data>
  <data name="Day" xml:space="preserve">
    <value>Gün</value>
    <comment>Single day unit</comment>
  </data>
  <data name="Days" xml:space="preserve">
    <value>Gün</value>
    <comment>Days label</comment>
  </data>
  <data name="Days Elapsed" xml:space="preserve">
    <value>Geçen Gün</value>
    <comment>Days elapsed column header</comment>
  </data>
  <data name="Days Held" xml:space="preserve">
    <value>Tutma Süresi</value>
    <comment>Label for number of days held</comment>
  </data>
  <data name="Days Remaining" xml:space="preserve">
    <value>Kalan Gün</value>
    <comment>Days remaining until maturity</comment>
  </data>
  <data name="Dealer" xml:space="preserve">
    <value>Bayi</value>
    <comment>Dealer label</comment>
  </data>
  <data name="Dealer Network" xml:space="preserve">
    <value>Bayi Ağı</value>
    <comment>Dealer network section title</comment>
  </data>
  <data name="Dealers" xml:space="preserve">
    <value>Bayiler</value>
    <comment>Dealers menu item</comment>
  </data>
  <data name="Decimal Places" xml:space="preserve">
    <value>Ondalık Basamaklar</value>
    <comment>Label for decimal places</comment>
  </data>
  <data name="Delete" xml:space="preserve">
    <value>Sil</value>
    <comment>Delete button</comment>
  </data>
  <data name="Delete Announcement" xml:space="preserve">
    <value>Duyuru Sil</value>
    <comment>Delete announcement button/title</comment>
  </data>
  <data name="Delete Balance Transaction" xml:space="preserve">
    <value>Bakiye İşlemini Sil</value>
    <comment>Title for deleting a balance transaction</comment>
  </data>
  <data name="Delete Package" xml:space="preserve">
    <value>Paketi Sil</value>
    <comment>Title for package delete page</comment>
  </data>
  <data name="Delete Package Reward Percentage" xml:space="preserve">
    <value>Paket Ödül Yüzdesini Sil</value>
    <comment>Title for package reward percentage delete page</comment>
  </data>
  <data name="Delete Plan" xml:space="preserve">
    <value>Plan Sil</value>
    <comment>Button text for deleting savings plan</comment>
  </data>
  <data name="Delete User Package" xml:space="preserve">
    <value>Kullanıcı Paketini Sil</value>
    <comment>Title for user package delete page</comment>
  </data>
  <data name="Deleting this trade will not update user balances" xml:space="preserve">
    <value>Bu işlemi silmek kullanıcı bakiyelerini güncellemeyecektir</value>
    <comment>Warning message when deleting a trade</comment>
  </data>
  <data name="Deletion was successful" xml:space="preserve">
    <value>Silme işlemi başarılı</value>
    <comment>Deletion success message</comment>
  </data>
  <data name="Demo account is expired. You should buy a paid license" xml:space="preserve">
    <value>Demo hesabı süresi doldu. Ücretli bir lisans satın almalısınız</value>
    <comment>Demo account expired message</comment>
  </data>
  <data name="Deposit" xml:space="preserve">
    <value>Para Yatırma</value>
    <comment>Deposit page title</comment>
  </data>
  <data name="Deposit Amount" xml:space="preserve">
    <value>Yatırım Tutarı</value>
    <comment>Deposit amount label</comment>
  </data>
  <data name="Deposit Approval Transaction Check" xml:space="preserve">
    <value>Para Yatırma Onay İşlemi Kontrolü</value>
    <comment>Check item for deposit approval transaction</comment>
  </data>
  <data name="Deposit can be reversed" xml:space="preserve">
    <value>Para yatırma işlemi geri alınabilir</value>
    <comment>Status message for eligible deposits</comment>
  </data>
  <data name="Deposit Funds" xml:space="preserve">
    <value>Para Yatır</value>
    <comment>Label for deposit funds button</comment>
  </data>
  <data name="Deposit has been successfully reversed" xml:space="preserve">
    <value>Para yatırma işlemi başarıyla geri alındı</value>
    <comment>Success message for deposit reversal</comment>
  </data>
  <data name="Deposit History" xml:space="preserve">
    <value>Para Yatırma Geçmişi</value>
    <comment>Deposit history section title</comment>
  </data>
  <data name="Deposit is not in approved state" xml:space="preserve">
    <value>Para yatırma işlemi onaylanmış durumunda değil</value>
    <comment>Error message for deposit status</comment>
  </data>
  <data name="Deposit must be in Approved state" xml:space="preserve">
    <value>Para yatırma işlemi Onaylanmış durumunda olmalıdır</value>
    <comment>Error message for deposit status check</comment>
  </data>
  <data name="Deposit reversal: {0}" xml:space="preserve">
    <value>Para Yatırma İptali: {0}</value>
    <comment>Description for deposit reversal transaction</comment>
  </data>
  <data name="Deposit Status Check" xml:space="preserve">
    <value>Para Yatırma Durumu Kontrolü</value>
    <comment>Check item for deposit status</comment>
  </data>
  <data name="Deposit to Your Account" xml:space="preserve">
    <value>Hesabınıza Para Yatırın!</value>
    <comment>Deposit step title</comment>
  </data>
  <data name="Deposit Transaction - User ID:" xml:space="preserve">
    <value>Para Yatırma İşlemi - Kullanıcı ID:</value>
    <comment>Deposit transaction description part</comment>
  </data>
  <data name="Deposit Transaction - User ID: {0}, User Name: {1}. Transaction Date: {2}" xml:space="preserve">
    <value>Para Yatırma İşlemi - Kullanıcı ID: {0}, Kullanıcı Adı: {1}. İşlem Tarihi: {2}</value>
    <comment>Deposit transaction description format</comment>
  </data>
  <data name="Deposit was approved on {0}" xml:space="preserve">
    <value>Para yatırma işlemi {0} tarihinde onaylanmış</value>
    <comment>Status message for deposit approval date</comment>
  </data>
  <data name="Deposit your crypto assets and earn interest with flexible terms." xml:space="preserve">
    <value>Kripto varlıklarınızı yatırın ve esnek koşullarla faiz kazanın.</value>
    <comment>Savings description</comment>
  </data>
  <data name="Deposit your RZW tokens and earn interest with flexible terms." xml:space="preserve">
    <value>RZW tokenlarınızı yatırın ve esnek koşullarla faiz kazanın.</value>
    <comment>Savings description for RZW</comment>
  </data>
  <data name="Deposit: {0}" xml:space="preserve">
    <value>Para Yatırma: {0}</value>
    <comment>Description for deposit transaction</comment>
  </data>
  <data name="Deposits" xml:space="preserve">
    <value>Para Yatırmalar</value>
    <comment>Used for deposit management in admin panel</comment>
  </data>
  <data name="Description" xml:space="preserve">
    <value>Açıklama</value>
    <comment>Description field label</comment>
  </data>
  <data name="Details" xml:space="preserve">
    <value>Detaylar</value>
    <comment>Details button</comment>
  </data>
  <data name="Device" xml:space="preserve">
    <value>Cihaz</value>
    <comment>Device label</comment>
  </data>
  <data name="Device Brand" xml:space="preserve">
    <value>Cihaz Markası</value>
    <comment>Device brand label</comment>
  </data>
  <data name="DigiSign. All rights reserved." xml:space="preserve">
    <value>DigiSign. Tüm hakları saklıdır.</value>
    <comment>Copyright text</comment>
  </data>
  <data name="Disable Auto Renew" xml:space="preserve">
    <value>Otomatik yenilemeyi devre dışı bırak</value>
    <comment>Otomatik yenilemeyi devre dışı bırak butonu</comment>
  </data>
  <data name="Disabled" xml:space="preserve">
    <value>Devre Dışı</value>
    <comment>Status label for disabled state</comment>
  </data>
  <data name="Display Order" xml:space="preserve">
    <value>Görüntüleme Sırası</value>
    <comment>Label for display order field</comment>
  </data>
  <data name="Distribute Rewards" xml:space="preserve">
    <value>Ödülleri Dağıt</value>
  </data>
  <data name="Distributed" xml:space="preserve">
    <value>Dağıtıldı</value>
  </data>
  <data name="Distributed Rewards" xml:space="preserve">
    <value>Dağıtılan Ödüller</value>
  </data>
  <data name="Download for Android" xml:space="preserve">
    <value>Android İçin İndirin</value>
    <comment>Android app download button</comment>
  </data>
  <data name="Download for iOS" xml:space="preserve">
    <value>iOS İçin İndirin</value>
    <comment>iOS app download button</comment>
  </data>
  <data name="Download Razewin App and Start Earning RZW Tokens While Traveling" xml:space="preserve">
    <value>Razewin uygulamasını indirin ve seyahat ederken RZW Token kazanmaya başlayın! Yapay zeka destekli navigasyon ile hem verimli seyahat edin hem de kazancınızı artırın.</value>
    <comment>Drive to earn slider description</comment>
  </data>
  <data name="Draft" xml:space="preserve">
    <value>Taslak</value>
    <comment>Draft status</comment>
  </data>
  <data name="Drive to Earn" xml:space="preserve">
    <value>SÜRÜŞ İLE KAZAN</value>
    <comment>Drive to earn slider title</comment>
  </data>
  <data name="Dynamic Content" xml:space="preserve">
    <value>Dinamik İçerik</value>
    <comment>Dynamic content title</comment>
  </data>
  <data name="Early Withdrawal Analysis" xml:space="preserve">
    <value>Erken Çekim Analizi</value>
    <comment>Section title for early withdrawal analysis</comment>
  </data>
  <data name="Early Withdrawal Available" xml:space="preserve">
    <value>Erken Çekim Mevcut</value>
    <comment>Title for early withdrawal section</comment>
  </data>
  <data name="Early withdrawal completed successfully. Amount withdrawn: {0} RZW" xml:space="preserve">
    <value>Erken çekim başarıyla tamamlandı. Çekilen miktar: {0} RZW</value>
    <comment>Success message for early withdrawal</comment>
  </data>
  <data name="Early Withdrawal Penalty" xml:space="preserve">
    <value>Erken Çekim Cezası</value>
    <comment>Label for early withdrawal penalty</comment>
  </data>
  <data name="Early Withdrawn" xml:space="preserve">
    <value>Erken Çekildi</value>
    <comment>Early withdrawal status</comment>
  </data>
  <data name="Earn Money" xml:space="preserve">
    <value>Para Kazan</value>
    <comment>Earn money button</comment>
  </data>
  <data name="Earn Money with Crypto" xml:space="preserve">
    <value>Kripto ile Para Kazan</value>
    <comment>Earn money page title</comment>
  </data>
  <data name="Earn Money with Cryptocurrency" xml:space="preserve">
    <value>Kripto Para ile Para Kazanın</value>
    <comment>Earn money section title</comment>
  </data>
  <data name="Earn passive income by holding and staking your cryptocurrencies." xml:space="preserve">
    <value>Kripto paralarınızı tutarak ve stake ederek pasif gelir elde edin.</value>
    <comment>Staking description</comment>
  </data>
  <data name="Earn passive income by holding and staking your RZW tokens." xml:space="preserve">
    <value>RZW tokenlarınızı tutarak ve stake ederek pasif gelir elde edin.</value>
    <comment>Staking description for RZW</comment>
  </data>
  <data name="Earn While Driving" xml:space="preserve">
    <value>SÜRÜŞ YAPARKEN KAZAN!</value>
    <comment>Earn while driving slider heading</comment>
  </data>
  <data name="EARN WHILE TRAVELING" xml:space="preserve">
    <value>SEYAHAT EDERKEN KAZANIN</value>
    <comment>RZW Token tagline</comment>
  </data>
  <data name="Earnings Cap" xml:space="preserve">
    <value>Kazanç Limiti</value>
    <comment>Label for earnings cap</comment>
  </data>
  <data name="Edit" xml:space="preserve">
    <value>Düzenle</value>
    <comment>Edit button text</comment>
  </data>
  <data name="Edit Announcement" xml:space="preserve">
    <value>Duyuru Düzenle</value>
    <comment>Edit announcement button/title</comment>
  </data>
  <data name="Edit Balance Transaction" xml:space="preserve">
    <value>Bakiye İşlemini Düzenle</value>
    <comment>Title for editing a balance transaction</comment>
  </data>
  <data name="Edit Bank" xml:space="preserve">
    <value>Bankayı Düzenle</value>
    <comment>Title for edit bank page</comment>
  </data>
  <data name="Edit Market" xml:space="preserve">
    <value>Piyasayı Düzenle</value>
    <comment>Title for edit market page</comment>
  </data>
  <data name="Edit Package" xml:space="preserve">
    <value>Paketi Düzenle</value>
    <comment>Title for package edit page</comment>
  </data>
  <data name="Edit Package Reward Percentage" xml:space="preserve">
    <value>Paket Ödül Yüzdesini Düzenle</value>
    <comment>Title for package reward percentage edit page</comment>
  </data>
  <data name="Edit Payment" xml:space="preserve">
    <value>Ödeme Düzenle</value>
    <comment>Edit payment section title</comment>
  </data>
  <data name="Edit Plan" xml:space="preserve">
    <value>Plan Düzenle</value>
    <comment>Button text for editing savings plan</comment>
  </data>
  <data name="Edit Setting" xml:space="preserve">
    <value>Ayarı Düzenle</value>
    <comment>Title for edit setting page</comment>
  </data>
  <data name="Edit Trade" xml:space="preserve">
    <value>İşlemi Düzenle</value>
    <comment>Title for edit trade page</comment>
  </data>
  <data name="Edit User Package" xml:space="preserve">
    <value>Kullanıcı Paketini Düzenle</value>
    <comment>Title for user package edit page</comment>
  </data>
  <data name="Edit Wallet" xml:space="preserve">
    <value>Cüzdanı Düzenle</value>
    <comment>Title for edit wallet page</comment>
  </data>
  <data name="Edit Withdrawal" xml:space="preserve">
    <value>Para Çekme İşlemini Düzenle</value>
    <comment>Title for edit withdrawal page</comment>
  </data>
  <data name="Effective APY" xml:space="preserve">
    <value>Efektif Yıllık Getiri</value>
    <comment>Label for effective annual percentage yield</comment>
  </data>
  <data name="Element not found" xml:space="preserve">
    <value>Element bulunamadı</value>
    <comment>Error message when element is not found</comment>
  </data>
  <data name="Eligible for Reversal" xml:space="preserve">
    <value>Geri Almaya Uygun</value>
    <comment>Status text for deposits eligible for reversal</comment>
  </data>
  <data name="Email" xml:space="preserve">
    <value>E-posta</value>
    <comment>Email label</comment>
  </data>
  <data name="Email Address" xml:space="preserve">
    <value>E-posta Adresi</value>
    <comment>Email address field label</comment>
  </data>
  <data name="Email Verification" xml:space="preserve">
    <value>E-posta Doğrulama</value>
    <comment>Email verification title</comment>
  </data>
  <data name="Email Verification Required" xml:space="preserve">
    <value>E-posta Doğrulama Gerekli</value>
    <comment>Email verification subject and header</comment>
  </data>
  <data name="Email Verified" xml:space="preserve">
    <value>Email Verified</value>
    <comment>Email verification status - verified</comment>
  </data>
  <data name="Email verified on" xml:space="preserve">
    <value>E-posta doğrulama tarihi</value>
    <comment>Verification date prefix</comment>
  </data>
  <data name="Emergency Close" xml:space="preserve">
    <value>Acil Kapatma</value>
    <comment>Button text for emergency account closure</comment>
  </data>
  <data name="Enable Auto Renew" xml:space="preserve">
    <value>Otomatik yenilemeyi etkinleştir</value>
    <comment>Otomatik yenilemeyi etkinleştir butonu</comment>
  </data>
  <data name="Enabled" xml:space="preserve">
    <value>Etkin</value>
    <comment>Status label for enabled state</comment>
  </data>
  <data name="Enter a valid IBAN" xml:space="preserve">
    <value>Geçerli bir IBAN girin</value>
    <comment>Validation message for IBAN</comment>
  </data>
  <data name="Enter account holder name" xml:space="preserve">
    <value>Hesap sahibi adını girin</value>
    <comment>Enter account holder name placeholder</comment>
  </data>
  <data name="Enter amount" xml:space="preserve">
    <value>Tutar girin</value>
    <comment>Enter amount placeholder</comment>
  </data>
  <data name="Enter amount to buy" xml:space="preserve">
    <value>Satın alınacak miktarı girin</value>
    <comment>Placeholder for amount to buy input</comment>
  </data>
  <data name="Enter amount to sell" xml:space="preserve">
    <value>Satılacak miktarı girin</value>
    <comment>Placeholder for amount to sell input</comment>
  </data>
  <data name="Enter amount to spend" xml:space="preserve">
    <value>Harcanacak miktarı girin</value>
    <comment>Placeholder for amount to spend input</comment>
  </data>
  <data name="Enter as percentage (e.g., 0.03 for 0.03%)" xml:space="preserve">
    <value>Yüzde olarak girin (örn. %0.03 için 0.03)</value>
    <comment>Help text for interest rate input</comment>
  </data>
  <data name="Enter Bank Transfer Time" xml:space="preserve">
    <value>Banka transferini gerçekleştirdiğiniz saati giriniz.</value>
    <comment>Transfer time field help text</comment>
  </data>
  <data name="Enter benefits as JSON array of features" xml:space="preserve">
    <value>Özellikleri JSON dizisi olarak girin</value>
    <comment>Label for enter benefits as JSON array of features</comment>
  </data>
  <data name="Enter decimal part" xml:space="preserve">
    <value>Ondalık kısmı giriniz</value>
    <comment>Placeholder for decimal part input</comment>
  </data>
  <data name="Enter Full Name" xml:space="preserve">
    <value>İsim soyisim giriniz</value>
    <comment>Full name placeholder</comment>
  </data>
  <data name="Enter IBAN" xml:space="preserve">
    <value>IBAN girin</value>
    <comment>Enter IBAN placeholder</comment>
  </data>
  <data name="Enter integer part" xml:space="preserve">
    <value>Tam kısmı giriniz</value>
    <comment>Placeholder for integer part input</comment>
  </data>
  <data name="Enter the address you sent from" xml:space="preserve">
    <value>Gönderdiğiniz adresi girin</value>
    <comment>Label for sender address field</comment>
  </data>
  <data name="Enter the amount and transaction reference in the form." xml:space="preserve">
    <value>Formdaki tutar ve işlem referansını girin.</value>
    <comment>Instructions for entering transaction details</comment>
  </data>
  <data name="Enter the amount you wish to withdraw." xml:space="preserve">
    <value>Çekmek istediğiniz tutarı girin.</value>
    <comment>Withdrawal step 1</comment>
  </data>
  <data name="Enter the transaction hash/ID from your wallet" xml:space="preserve">
    <value>Cüzdanınızdan işlem hash/ID'sini girin</value>
    <comment>Label for transaction hash field</comment>
  </data>
  <data name="Enter transaction reference" xml:space="preserve">
    <value>İşlem referansını girin</value>
    <comment>Placeholder for transaction reference field</comment>
  </data>
  <data name="Enter Valid Format" xml:space="preserve">
    <value>Lütfen geçerli bir format giriniz</value>
    <comment>Validation message for format</comment>
  </data>
  <data name="Enter Valid IBAN" xml:space="preserve">
    <value>Lütfen geçerli bir IBAN giriniz</value>
    <comment>Validation message for IBAN format</comment>
  </data>
  <data name="Enter your bank account IBAN for withdrawals (e.g., TR00 0000 0000 0000 0000 0000 00)" xml:space="preserve">
    <value>Para çekme işlemleri için banka hesabı IBAN'ınızı girin (örn. TR00 0000 0000 0000 0000 0000 00)</value>
    <comment>Placeholder for IBAN field in withdrawal form</comment>
  </data>
  <data name="Enter Your Full Name" xml:space="preserve">
    <value>İsminizi soyadınızı giriniz</value>
    <comment>Sender full name placeholder</comment>
  </data>
  <data name="Entities" xml:space="preserve">
    <value>Varlıklar</value>
    <comment>Entities section header</comment>
  </data>
  <data name="Error" xml:space="preserve">
    <value>Hata</value>
    <comment>Error title for alerts</comment>
  </data>
  <data name="Error changing password" xml:space="preserve">
    <value>Şifre değiştirme hatası</value>
    <comment>Error message for password change</comment>
  </data>
  <data name="Error copying to clipboard" xml:space="preserve">
    <value>Panoya kopyalama hatası</value>
    <comment>Error message when copying to clipboard fails</comment>
  </data>
  <data name="Error loading pairs" xml:space="preserve">
    <value>Çiftler yüklenirken hata oluştu</value>
    <comment>Error message when loading trading pairs fails</comment>
  </data>
  <data name="Error submitting payment: {0}" xml:space="preserve">
    <value>Ödeme gönderilirken hata oluştu: {0}</value>
    <comment>Error message for payment submission with parameter</comment>
  </data>
  <data name="Error submitting withdrawal: {0}" xml:space="preserve">
    <value>Para çekme talebi gönderilirken hata oluştu: {0}</value>
    <comment>Error message for withdrawal submission with parameter</comment>
  </data>
  <data name="Error updating profile" xml:space="preserve">
    <value>Profil güncelleme hatası</value>
    <comment>Error message for profile update</comment>
  </data>
  <data name="Error validating IBAN" xml:space="preserve">
    <value>IBAN doğrulama hatası</value>
    <comment>Error message when IBAN validation fails</comment>
  </data>
  <data name="Example Time Format" xml:space="preserve">
    <value>Örn: 14:30</value>
    <comment>Transfer time field placeholder</comment>
  </data>
  <data name="Exit" xml:space="preserve">
    <value>Çıkış</value>
    <comment>Exit button text</comment>
  </data>
  <data name="Expired" xml:space="preserve">
    <value>Süresi Doldu</value>
    <comment>Label for expired status</comment>
  </data>
  <data name="Expiry Date" xml:space="preserve">
    <value>Son Kullanma Tarihi</value>
    <comment>Label for expiry date</comment>
  </data>
  <data name="Explore Our Solutions" xml:space="preserve">
    <value>Çözümlerimizi Keşfedin</value>
    <comment>Explore solutions button</comment>
  </data>
  <data name="Export CSV" xml:space="preserve">
    <value>CSV Dışa Aktar</value>
    <comment>Export to CSV button text</comment>
  </data>
  <data name="Export Excel" xml:space="preserve">
    <value>Excel Dışa Aktar</value>
    <comment>Export to Excel button text</comment>
  </data>
  <data name="Extra Data" xml:space="preserve">
    <value>Ek Bilgiler</value>
    <comment>Extra data label</comment>
  </data>
  <data name="Failed" xml:space="preserve">
    <value>Başarısız</value>
    <comment>Failed status</comment>
  </data>
  <data name="Failed to copy" xml:space="preserve">
    <value>Kopyalama başarısız</value>
    <comment>Error message when copying to clipboard fails</comment>
  </data>
  <data name="Failed to copy text" xml:space="preserve">
    <value>Metin kopyalanamadı</value>
    <comment>Error message when copying text fails</comment>
  </data>
  <data name="Failed to reverse deposit" xml:space="preserve">
    <value>Para yatırma işlemi geri alınamadı</value>
    <comment>Error message for failed deposit reversal</comment>
  </data>
  <data name="Failed to send verification email. Please try again later." xml:space="preserve">
    <value>Doğrulama e-postası gönderilemedi. Lütfen daha sonra tekrar deneyin.</value>
    <comment>Error message for email send failure</comment>
  </data>
  <data name="Failed to update payment status" xml:space="preserve">
    <value>Ödeme durumu güncellenemedi</value>
    <comment>Error message when payment status update fails</comment>
  </data>
  <data name="Failed to update withdrawal status" xml:space="preserve">
    <value>Para çekme durumu güncellenemedi</value>
    <comment>Error message when withdrawal status update fails</comment>
  </data>
  <data name="Failure Count" xml:space="preserve">
    <value>Başarısızlık Sayısı</value>
    <comment>Number of failed executions of background service</comment>
  </data>
  <data name="Fallback copy error" xml:space="preserve">
    <value>Yedek kopyalama hatası</value>
    <comment>Error message when fallback copy method fails</comment>
  </data>
  <data name="Fallback copy failed" xml:space="preserve">
    <value>Yedek kopyalama başarısız</value>
    <comment>Error message when fallback copy method fails</comment>
  </data>
  <data name="Fast Withdrawals" xml:space="preserve">
    <value>HIZLI ÇEKİMLER</value>
    <comment>Fast withdrawals text</comment>
  </data>
  <data name="Features" xml:space="preserve">
    <value>Özellikler</value>
    <comment>Features menu item</comment>
  </data>
  <data name="Fees Text" xml:space="preserve">
    <value>Platform üzerinde gerçekleştirilen işlemler için belirli ücretler alınmaktadır. Güncel ücret bilgilerine web sitemizden ulaşabilirsiniz.</value>
    <comment>Text for fees section in user agreement</comment>
  </data>
  <data name="Fees Title" xml:space="preserve">
    <value>Ücretler</value>
    <comment>Title for fees section in user agreement</comment>
  </data>
  <data name="Fiat Currency Deposit Methods" xml:space="preserve">
    <value>Fiat Para Yatırma Yöntemleri</value>
    <comment>Header for fiat currency deposit methods section</comment>
  </data>
  <data name="Fiat Deposits" xml:space="preserve">
    <value>Fiat Para Yatırma</value>
    <comment>Label for fiat currency deposits section</comment>
  </data>
  <data name="File Width" xml:space="preserve">
    <value>Dosya Genişliği</value>
    <comment>File width label</comment>
  </data>
  <data name="Files Count" xml:space="preserve">
    <value>Dosya Sayısı</value>
    <comment>Files count label</comment>
  </data>
  <data name="Filter" xml:space="preserve">
    <value>Filtrele</value>
    <comment>Filter button text</comment>
  </data>
  <data name="Filtered from _MAX_ total entries" xml:space="preserve">
    <value>(_MAX_ kayıt içerisinden filtrelendi)</value>
    <comment>DataTables info filtered text</comment>
  </data>
  <data name="Filtered Results" xml:space="preserve">
    <value>Filtrelenmiş Sonuçlar</value>
    <comment>Number of filtered results</comment>
  </data>
  <data name="Final Amount" xml:space="preserve">
    <value>Son Miktar</value>
    <comment>Label for final amount after interest</comment>
  </data>
  <data name="First" xml:space="preserve">
    <value>İlk</value>
    <comment>First page button text</comment>
  </data>
  <data name="First Deposit Bonus" xml:space="preserve">
    <value>İLK YATIRIM BONUSU!</value>
    <comment>First deposit bonus title</comment>
  </data>
  <data name="First Name" xml:space="preserve">
    <value>Ad</value>
    <comment>First name field label</comment>
  </data>
  <data name="Flexible terms and withdrawal options" xml:space="preserve">
    <value>Esnek koşullar ve çekim seçenekleri</value>
    <comment>Benefit 3</comment>
  </data>
  <data name="For each referrer with an active package, the system will calculate a reward based on the package's reward percentage." xml:space="preserve">
    <value>Aktif paketi olan her referans için sistem, paketin ödül yüzdesine göre bir ödül hesaplayacaktır.</value>
  </data>
  <data name="Force Maturity" xml:space="preserve">
    <value>Zorla Vade Doldurun</value>
    <comment>Button text for forcing account maturity</comment>
  </data>
  <data name="From Date" xml:space="preserve">
    <value>Başlangıç Tarihi</value>
    <comment>From date filter label</comment>
  </data>
  <data name="Full Name" xml:space="preserve">
    <value>Adınız Soyadınız</value>
    <comment>Full name label</comment>
  </data>
  <data name="Full Name Required" xml:space="preserve">
    <value>Ad soyad gereklidir</value>
    <comment>Full name validation message</comment>
  </data>
  <data name="Future Investment Begins" xml:space="preserve">
    <value>GELECEĞiN YATIRIMI BAŞLIYOR</value>
    <comment>Title for the second slider</comment>
  </data>
  <data name="General Increase" xml:space="preserve">
    <value>Genel Artış</value>
    <comment>Label for general increase in statistics</comment>
  </data>
  <data name="Generate Report" xml:space="preserve">
    <value>Rapor Oluştur</value>
    <comment>Button text for generating reports</comment>
  </data>
  <data name="Get More Information" xml:space="preserve">
    <value>Daha Fazla Bilgi Alın</value>
    <comment>Button text for getting more information</comment>
  </data>
  <data name="Go to Home Page" xml:space="preserve">
    <value>Ana Sayfaya Git</value>
    <comment>Link text for going to home page</comment>
  </data>
  <data name="Go To Papara" xml:space="preserve">
    <value>Papara'ya Git</value>
    <comment>Go to Papara button text</comment>
  </data>
  <data name="Group" xml:space="preserve">
    <value>Grup</value>
    <comment>Label for group field</comment>
  </data>
  <data name="Happy Customers" xml:space="preserve">
    <value>Mutlu Müşteriler</value>
    <comment>Happy customers label</comment>
  </data>
  <data name="Heat Map" xml:space="preserve">
    <value>Isı Haritası</value>
    <comment>Heat Map page title</comment>
  </data>
  <data name="Hello" xml:space="preserve">
    <value>Merhaba</value>
    <comment>Greeting in email</comment>
  </data>
  <data name="Hide Inactive Accounts" xml:space="preserve">
    <value>Pasif Hesapları Gizle</value>
    <comment>Button text to hide inactive savings accounts</comment>
  </data>
  <data name="Home" xml:space="preserve">
    <value>Ana Sayfa</value>
    <comment>Home link text</comment>
  </data>
  <data name="I forgot my password" xml:space="preserve">
    <value>Şifremi unuttum</value>
    <comment>Link text for password recovery</comment>
  </data>
  <data name="I Made The Deposit" xml:space="preserve">
    <value>Yatırımı Gerçekleştirdim</value>
    <comment>Deposit completion button text</comment>
  </data>
  <data name="IBAN" xml:space="preserve">
    <value>IBAN</value>
    <comment>Label for IBAN field</comment>
  </data>
  <data name="IBAN cannot exceed 50 characters" xml:space="preserve">
    <value>IBAN 50 karakteri geçemez</value>
    <comment>Validation message for IBAN length</comment>
  </data>
  <data name="IBAN Too Long" xml:space="preserve">
    <value>IBAN 50 karakteri geçemez</value>
    <comment>Validation message for IBAN length</comment>
  </data>
  <data name="IBAN will be automatically formatted" xml:space="preserve">
    <value>IBAN otomatik olarak biçimlendirilecektir</value>
    <comment>Help text for IBAN input</comment>
  </data>
  <data name="Icon" xml:space="preserve">
    <value>İkon</value>
    <comment>Label for icon field</comment>
  </data>
  <data name="Icon Image" xml:space="preserve">
    <value>İkon Resmi</value>
    <comment>Label for icon image field</comment>
  </data>
  <data name="Identity already exists" xml:space="preserve">
    <value>Kimlik zaten mevcut</value>
    <comment>Error message when identity already exists</comment>
  </data>
  <data name="Identity Number" xml:space="preserve">
    <value>Kimlik Numarası</value>
    <comment>Identity number field label</comment>
  </data>
  <data name="If checked, a balance transaction will be recorded for this purchase." xml:space="preserve">
    <value>İşaretlenirse, bu satın alma için bir bakiye işlemi kaydedilecektir.</value>
    <comment>Help text for balance transaction checkbox</comment>
  </data>
  <data name="If checked, a balance transaction will be recorded for this purchase. Note: Referral rewards for package purchases are disabled." xml:space="preserve">
    <value>İşaretlenirse, bu satın alma için bir bakiye işlemi kaydedilecektir. Not: Paket satın alımları için referans ödülleri devre dışı bırakılmıştır.</value>
    <comment>Help text for balance transaction checkbox</comment>
  </data>
  <data name="If checked, referral rewards will be processed for this purchase" xml:space="preserve">
    <value>İşaretlenirse, bu satın alma için referans ödülleri işlenecektir</value>
    <comment>Label for if checked, referral rewards will be processed for this purchase</comment>
  </data>
  <data name="If the button doesn't work, you can copy and paste this link into your browser:" xml:space="preserve">
    <value>Buton çalışmıyorsa, bu bağlantıyı kopyalayıp tarayıcınıza yapıştırabilirsiniz:</value>
    <comment>Alternative verification instruction</comment>
  </data>
  <data name="If this withdrawal was approved deleting it will not refund the users balance" xml:space="preserve">
    <value>If this withdrawal was approved, deleting it will not refund the user's balance</value>
    <comment>Warning message when deleting an approved withdrawal</comment>
  </data>
  <data name="If this withdrawal was approved, deleting it will not refund the user's balance" xml:space="preserve">
    <value>Bu ├ºekim onayland─▒ysa, silme i┼ƒlemi kullan─▒c─▒n─▒n bakiyesini iade etmeyecektir</value>
    <comment>Warning message when deleting an approved withdrawal</comment>
  </data>
  <data name="If you believe this is an error, please contact the site administrator" xml:space="preserve">
    <value>Bunun bir hata olduğunu düşünüyorsanız, lütfen site yöneticisiyle iletişime geçin</value>
    <comment>Message for users encountering errors</comment>
  </data>
  <data name="If you didn't create an account with us, please ignore this email." xml:space="preserve">
    <value>Bizde bir hesap oluşturmadıysanız, lütfen bu e-postayı dikkate almayın.</value>
    <comment>Security notice</comment>
  </data>
  <data name="Important" xml:space="preserve">
    <value>ÖNEMLİ</value>
    <comment>Important notice header</comment>
  </data>
  <data name="Important Note" xml:space="preserve">
    <value>Önemli Not</value>
    <comment>Important note label</comment>
  </data>
  <data name="Important!" xml:space="preserve">
    <value>Önemli!</value>
    <comment>Important notice title</comment>
  </data>
  <data name="Inactive" xml:space="preserve">
    <value>Pasif</value>
    <comment>Inactive status</comment>
  </data>
  <data name="Inactive Savings Accounts" xml:space="preserve">
    <value>Pasif Tasarruf Hesapları</value>
    <comment>Title for inactive savings accounts section</comment>
  </data>
  <data name="Increased Visibility" xml:space="preserve">
    <value>Artan Görünürlük</value>
    <comment>Increased visibility title</comment>
  </data>
  <data name="Initial Amount" xml:space="preserve">
    <value>Başlangıç Miktarı</value>
    <comment>Label for initial investment amount</comment>
  </data>
  <data name="Instant Account Credit" xml:space="preserve">
    <value>Anında Hesaba Yansır</value>
    <comment>Instant account credit text</comment>
  </data>
  <data name="Insufficient Balance" xml:space="preserve">
    <value>Yetersiz Bakiye</value>
    <comment>Button text when user has insufficient balance</comment>
  </data>
  <data name="Insufficient balance for this withdrawal" xml:space="preserve">
    <value>Bu çekim için yetersiz bakiye</value>
    <comment>Error message for insufficient balance</comment>
  </data>
  <data name="Insufficient Balance For Withdrawal" xml:space="preserve">
    <value>Bu çekim için yetersiz bakiye. Çekilebilir limitiniz: {0} {1}</value>
    <comment>Error message when user tries to withdraw more than available</comment>
  </data>
  <data name="Insufficient balance to purchase this package" xml:space="preserve">
    <value>Bu paketi satın almak için yetersiz bakiye</value>
    <comment>Error message for insufficient balance</comment>
  </data>
  <data name="Insufficient cryptocurrency balance for reversal" xml:space="preserve">
    <value>İşlemi geri almak için yetersiz kripto para bakiyesi</value>
    <comment>Error message when user doesn't have enough balance for a reversal</comment>
  </data>
  <data name="Interest Amount" xml:space="preserve">
    <value>Faiz Miktarı</value>
    <comment>Interest amount column header</comment>
  </data>
  <data name="Interest can only be claimed for matured and active accounts." xml:space="preserve">
    <value>Faiz sadece vadesi dolmuş ve aktif hesaplar için alınabilir.</value>
    <comment>Error message for invalid claim attempt.</comment>
  </data>
  <data name="Interest claimed successfully. Your principal and interest have been transferred to your balance." xml:space="preserve">
    <value>Faiz başarıyla alındı. Anapara ve faiz bakiyenize aktarıldı.</value>
    <comment>Success message after claiming matured interest.</comment>
  </data>
  <data name="Interest History" xml:space="preserve">
    <value>Faiz Geçmişi</value>
    <comment>Interest payment history</comment>
  </data>
  <data name="Interest Payment History" xml:space="preserve">
    <value>Faiz Ödeme Geçmişi</value>
    <comment>Interest payment history section title</comment>
  </data>
  <data name="Interest Preview" xml:space="preserve">
    <value>Faiz Önizlemesi</value>
    <comment>Section title for interest calculation preview</comment>
  </data>
  <data name="Interest Projections" xml:space="preserve">
    <value>Faiz Projeksiyonları</value>
    <comment>Section title for interest projections</comment>
  </data>
  <data name="Interest Rate" xml:space="preserve">
    <value>Faiz Oranı</value>
    <comment>Interest rate label</comment>
  </data>
  <data name="Invalid cryptocurrency data format" xml:space="preserve">
    <value>Geçersiz kripto para veri formatı</value>
    <comment>Error message for invalid cryptocurrency data format</comment>
  </data>
  <data name="Invalid cryptocurrency type" xml:space="preserve">
    <value>Geçersiz kripto para türü</value>
    <comment>Error message for invalid cryptocurrency type</comment>
  </data>
  <data name="Invalid interest rate for savings account" xml:space="preserve">
    <value>Tasarruf hesabı için geçersiz faiz oranı</value>
    <comment>Validation message for invalid account interest rate</comment>
  </data>
  <data name="Invalid login attempt" xml:space="preserve">
    <value>Geçersiz giriş denemesi</value>
    <comment>Error message for invalid login attempt</comment>
  </data>
  <data name="Invalid referral code" xml:space="preserve">
    <value>Geçersiz referans kodu</value>
    <comment>Error message for invalid referral code</comment>
  </data>
  <data name="Invalid term duration for savings account" xml:space="preserve">
    <value>Tasarruf hesabı için geçersiz vade süresi</value>
    <comment>Validation message for invalid account term duration</comment>
  </data>
  <data name="Invalid term type for savings account" xml:space="preserve">
    <value>Tasarruf hesabı için geçersiz vade türü</value>
    <comment>Validation message for invalid account term type</comment>
  </data>
  <data name="Invest with RazeWin" xml:space="preserve">
    <value>RazeWin ile Yatırım Yapın</value>
    <comment>Description for live price stream section</comment>
  </data>
  <data name="Investment Amount" xml:space="preserve">
    <value>Yatırım Miktarı</value>
    <comment>Section title for amount input</comment>
  </data>
  <data name="Invite Limit" xml:space="preserve">
    <value>Davet Limiti</value>
    <comment>Label for invite limit</comment>
  </data>
  <data name="Invite Limit Information" xml:space="preserve">
    <value>Davet Limiti Bilgisi</value>
    <comment>Section title for invite limit information</comment>
  </data>
  <data name="Invite Usage" xml:space="preserve">
    <value>Davet Kullanımı</value>
    <comment>Label for invite usage progress bar</comment>
  </data>
  <data name="Is Active" xml:space="preserve">
    <value>Aktif mi</value>
    <comment>Label for active status field</comment>
  </data>
  <data name="Is API" xml:space="preserve">
    <value>API mi</value>
    <comment>Label for API status field</comment>
  </data>
  <data name="Jet Speed Withdrawal" xml:space="preserve">
    <value>JET HIZINDA ÇEKİM!</value>
    <comment>Jet speed withdrawal title</comment>
  </data>
  <data name="Join Now" xml:space="preserve">
    <value>Hemen Katıl</value>
    <comment>Join now button</comment>
  </data>
  <data name="Join Our Growing Community" xml:space="preserve">
    <value>Büyüyen Topluluğumuza Katılın</value>
    <comment>Community section title</comment>
  </data>
  <data name="Join our referral program and start earning rewards by inviting others to RazeWin." xml:space="preserve">
    <value>Referans programımıza katılın ve başkalarını RazeWin'e davet ederek ödüller kazanmaya başlayın.</value>
    <comment>Package page description</comment>
  </data>
  <data name="JSON Format" xml:space="preserve">
    <value>JSON Formatı</value>
    <comment>Label for JSON format</comment>
  </data>
  <data name="Key" xml:space="preserve">
    <value>Anahtar</value>
    <comment>Label for key field</comment>
  </data>
  <data name="Last" xml:space="preserve">
    <value>Son</value>
    <comment>Last page button text</comment>
  </data>
  <data name="Last {0} transactions" xml:space="preserve">
    <value>Son {0} işlem</value>
    <comment>Parameterized message for showing last N transactions</comment>
  </data>
  <data name="Last Error" xml:space="preserve">
    <value>Son Hata</value>
    <comment>Last error message from background service</comment>
  </data>
  <data name="Last Name" xml:space="preserve">
    <value>Soyad</value>
    <comment>Last name field label</comment>
  </data>
  <data name="Last Online Date" xml:space="preserve">
    <value>Son Çevrimiçi Tarihi</value>
    <comment>Label for last online date field</comment>
  </data>
  <data name="Last Price Update" xml:space="preserve">
    <value>Son Fiyat Güncelleme</value>
    <comment>Last price update column header</comment>
  </data>
  <data name="Last Run Time" xml:space="preserve">
    <value>Son Çalışma Zamanı</value>
    <comment>Label for last run time</comment>
  </data>
  <data name="Last Success" xml:space="preserve">
    <value>Son Başarı</value>
    <comment>Last successful run time of background service</comment>
  </data>
  <data name="Last updated" xml:space="preserve">
    <value>Son güncelleme</value>
    <comment>Label for last updated field</comment>
  </data>
  <data name="Latest Announcements" xml:space="preserve">
    <value>Son Duyurular</value>
    <comment>Latest announcements section title</comment>
  </data>
  <data name="Learn more about our company" xml:space="preserve">
    <value>Şirketimiz hakkında daha fazla bilgi edinin</value>
    <comment>About page subtitle</comment>
  </data>
  <data name="Leave empty for unlimited" xml:space="preserve">
    <value>Sınırsız için boş bırakın</value>
    <comment>Label for leave empty for unlimited</comment>
  </data>
  <data name="Legal Compliance Text" xml:space="preserve">
    <value>Kullanıcılar, kripto para işlemlerinde tüm yerel yasalara ve düzenlemelere uymakla yükümlüdür. RAZEWIN, gerektiğinde yasal otoritelere bilgi sağlama hakkını saklı tutar.</value>
    <comment>Text for legal compliance section in user agreement</comment>
  </data>
  <data name="Legal Compliance Title" xml:space="preserve">
    <value>Yasal Uyumluluk</value>
    <comment>Title for legal compliance section in user agreement</comment>
  </data>
  <data name="Lend your crypto assets to earn interest while helping others." xml:space="preserve">
    <value>Başkalarına yardım ederken faiz kazanmak için kripto varlıklarınızı ödünç verin.</value>
    <comment>Lending description</comment>
  </data>
  <data name="Lend your RZW tokens to earn interest while helping others." xml:space="preserve">
    <value>Başkalarına yardım ederken faiz kazanmak için RZW tokenlarınızı ödünç verin.</value>
    <comment>Lending description for RZW</comment>
  </data>
  <data name="Lending" xml:space="preserve">
    <value>Borç Verme</value>
    <comment>Lending title</comment>
  </data>
  <data name="Less than 1 day" xml:space="preserve">
    <value>1 günden az</value>
    <comment>Displayed when savings account has less than 1 day remaining</comment>
  </data>
  <data name="Level" xml:space="preserve">
    <value>Seviye</value>
    <comment>Label for level</comment>
  </data>
  <data name="Level {0} percentage ({1}%) cannot be greater than Level {2} percentage ({3}%)" xml:space="preserve">
    <value>Seviye {0} yüzdesi ({1}%), Seviye {2} yüzdesinden ({3}%) büyük olamaz</value>
    <comment>Error message for package reward percentage validation</comment>
  </data>
  <data name="Level 1 (Direct Referral)" xml:space="preserve">
    <value>Seviye 1 (Doğrudan Referans)</value>
    <comment>Label for level 1 (direct referral)</comment>
  </data>
  <data name="Level 1 (Direct)" xml:space="preserve">
    <value>Seviye 1 (Doğrudan)</value>
    <comment>Label for level 1 (direct referral)</comment>
  </data>
  <data name="Level 10" xml:space="preserve">
    <value>Seviye 10</value>
    <comment>Level 10 for package reward percentages</comment>
  </data>
  <data name="Level 2 (Indirect Referral)" xml:space="preserve">
    <value>Seviye 2 (Dolaylı Referans)</value>
    <comment>Label for level 2 (indirect referral)</comment>
  </data>
  <data name="Level 2 (Indirect)" xml:space="preserve">
    <value>Seviye 2 (Dolaylı)</value>
    <comment>Label for level 2 (indirect referral)</comment>
  </data>
  <data name="Level 3" xml:space="preserve">
    <value>Seviye 3</value>
    <comment>Level 3 for package reward percentages</comment>
  </data>
  <data name="Level 4" xml:space="preserve">
    <value>Seviye 4</value>
    <comment>Level 4 for package reward percentages</comment>
  </data>
  <data name="Level 5" xml:space="preserve">
    <value>Seviye 5</value>
    <comment>Level 5 for package reward percentages</comment>
  </data>
  <data name="Level 6" xml:space="preserve">
    <value>Seviye 6</value>
    <comment>Level 6 for package reward percentages</comment>
  </data>
  <data name="Level 7" xml:space="preserve">
    <value>Seviye 7</value>
    <comment>Level 7 for package reward percentages</comment>
  </data>
  <data name="Level 8" xml:space="preserve">
    <value>Seviye 8</value>
    <comment>Level 8 for package reward percentages</comment>
  </data>
  <data name="Level 9" xml:space="preserve">
    <value>Seviye 9</value>
    <comment>Level 9 for package reward percentages</comment>
  </data>
  <data name="LISTING" xml:space="preserve">
    <value>LİSTELENME</value>
    <comment>Listing text for RZW Token slider</comment>
  </data>
  <data name="LISTING DATE" xml:space="preserve">
    <value>LİSTELENME TARİHİ</value>
    <comment>Listing date text for RZW Token slider</comment>
  </data>
  <data name="Live Price Stream" xml:space="preserve">
    <value>Canlı Fiyat Akışı</value>
    <comment>Title for live price stream section</comment>
  </data>
  <data name="Live Support" xml:space="preserve">
    <value>Canlı Destek</value>
    <comment>Live support link text</comment>
  </data>
  <data name="Loading..." xml:space="preserve">
    <value>Yükleniyor...</value>
    <comment>Text shown during loading operations</comment>
  </data>
  <data name="Locked Balance" xml:space="preserve">
    <value>Kilitli Bakiye</value>
    <comment>Locked balance label</comment>
  </data>
  <data name="Locked In Savings" xml:space="preserve">
    <value>Tasarruf hesabında Kilitli</value>
    <comment>Amount locked in savings accounts</comment>
  </data>
  <data name="Log in" xml:space="preserve">
    <value>Giriş Yap</value>
    <comment>Log in button text</comment>
  </data>
  <data name="Login" xml:space="preserve">
    <value>Giriş Yap</value>
    <comment>Login button/link text</comment>
  </data>
  <data name="Login As Selected User" xml:space="preserve">
    <value>Seçili Kullanıcı Olarak Giriş Yap</value>
    <comment>Button text for logging in as selected user</comment>
  </data>
  <data name="Login As User" xml:space="preserve">
    <value>Kullanıcı Olarak Giriş Yap</value>
    <comment>Title for the Login As User page</comment>
  </data>
  <data name="Login Now" xml:space="preserve">
    <value>Şimdi Giriş Yap!</value>
    <comment>Login now title</comment>
  </data>
  <data name="Login to My Account" xml:space="preserve">
    <value>Hesabıma Giriş Yap</value>
    <comment>Login button text</comment>
  </data>
  <data name="Login To Papara Account To Make Deposit" xml:space="preserve">
    <value>Papara Hesabınıza Giriş Yaparak Ekranda Bulunan Güncel Papara Hesabımıza Yatırımınızı Yapabilirsiniz</value>
    <comment>Papara deposit instructions</comment>
  </data>
  <data name="Login to Purchase" xml:space="preserve">
    <value>Satın Almak için Giriş Yapın</value>
    <comment>Button text for non-logged in users</comment>
  </data>
  <data name="Logout" xml:space="preserve">
    <value>Çıkış</value>
    <comment>Logout button text</comment>
  </data>
  <data name="Lower Package" xml:space="preserve">
    <value>Alt Paket</value>
    <comment>Used for packages lower than the user's current package</comment>
  </data>
  <data name="Main Agreement" xml:space="preserve">
    <value>Ana Sözleşme</value>
    <comment>Main agreement title</comment>
  </data>
  <data name="Main Agreement Read" xml:space="preserve">
    <value>Ana Sözleşmeyi Okudum</value>
    <comment>Main agreement checkbox label</comment>
  </data>
  <data name="Make Deposit Via Bank Transfer" xml:space="preserve">
    <value>Banka Hesaplarımıza Havale/EFT Yaparak Yatırımınızı Gerçekleştirebilirsiniz</value>
    <comment>Bank transfer deposit instructions</comment>
  </data>
  <data name="Make Deposit With" xml:space="preserve">
    <value>ile yatırım yap</value>
    <comment>Text for deposit method</comment>
  </data>
  <data name="Management Panel" xml:space="preserve">
    <value>Yönetim Paneli</value>
    <comment>Management panel title</comment>
  </data>
  <data name="Market" xml:space="preserve">
    <value>Piyasa</value>
    <comment>Label for market field</comment>
  </data>
  <data name="Market deletion is disabled" xml:space="preserve">
    <value>Market silme işlemi devre dışı bırakılmıştır</value>
    <comment>Message when trying to delete a market</comment>
  </data>
  <data name="Markets" xml:space="preserve">
    <value>Piyasalar</value>
    <comment>Markets page title</comment>
  </data>
  <data name="Matured" xml:space="preserve">
    <value>Vadesi Dolmuş</value>
    <comment>Status when savings account has matured</comment>
  </data>
  <data name="Matured Accounts" xml:space="preserve">
    <value>Vadesi Dolan Hesaplar</value>
    <comment>Label for matured accounts</comment>
  </data>
  <data name="Maturity Date" xml:space="preserve">
    <value>Vade Tarihi</value>
    <comment>Savings account maturity date</comment>
  </data>
  <data name="Maturity Reached! Claim your interest:" xml:space="preserve">
    <value>Vade Doldu! Faizinizi alın:</value>
    <comment>Alert for matured savings account, prompting user to claim interest.</comment>
  </data>
  <data name="Max" xml:space="preserve">
    <value>Maks</value>
    <comment>Maximum amount label</comment>
  </data>
  <data name="Max Amount" xml:space="preserve">
    <value>Max Tutar</value>
    <comment>Label for maximum amount field</comment>
  </data>
  <data name="Maximum" xml:space="preserve">
    <value>Maksimum</value>
    <comment>Maximum label</comment>
  </data>
  <data name="Maximum Buy" xml:space="preserve">
    <value>Maksimum Alım</value>
    <comment>Label for maximum buy amount field</comment>
  </data>
  <data name="Maximum Sell" xml:space="preserve">
    <value>Maksimum Satış</value>
    <comment>Label for maximum sell amount field</comment>
  </data>
  <data name="Meet Our Global User Network!" xml:space="preserve">
    <value>Küresel Kullanıcı Ağımızla Tanış!</value>
    <comment>Global user network text</comment>
  </data>
  <data name="Member Since" xml:space="preserve">
    <value>Üyelik Tarihi</value>
    <comment>Label for member registration date</comment>
  </data>
  <data name="Min" xml:space="preserve">
    <value>Min</value>
    <comment>Minimum amount label</comment>
  </data>
  <data name="Min Amount" xml:space="preserve">
    <value>Min Tutar</value>
    <comment>Label for minimum amount field</comment>
  </data>
  <data name="Minimum amount" xml:space="preserve">
    <value>Minimum tutar</value>
    <comment>Minimum amount label</comment>
  </data>
  <data name="Minimum Buy" xml:space="preserve">
    <value>Minimum Alım</value>
    <comment>Label for minimum buy amount field</comment>
  </data>
  <data name="Minimum Sell" xml:space="preserve">
    <value>Minimum Satış</value>
    <comment>Label for minimum sell amount field</comment>
  </data>
  <data name="Missing cryptocurrency information in payment data" xml:space="preserve">
    <value>Ödeme verilerinde eksik kripto para bilgisi</value>
    <comment>Error message for missing cryptocurrency information</comment>
  </data>
  <data name="Mission" xml:space="preserve">
    <value>Misyon</value>
    <comment>Mission title</comment>
  </data>
  <data name="Mission &amp; Vision" xml:space="preserve">
    <value>Misyon &amp; Vizyon</value>
    <comment>Label for mission and vision section</comment>
  </data>
  <data name="Modified Date" xml:space="preserve">
    <value>Değiştirilme Tarihi</value>
    <comment>Label for modified date field</comment>
  </data>
  <data name="Monitoring" xml:space="preserve">
    <value>İzleme</value>
    <comment>Admin menu item for system monitoring</comment>
  </data>
  <data name="Month" xml:space="preserve">
    <value>Ay</value>
    <comment>Singular form of month</comment>
  </data>
  <data name="Monthly" xml:space="preserve">
    <value>Aylık</value>
    <comment>Monthly period label</comment>
  </data>
  <data name="Monthly Breakdown" xml:space="preserve">
    <value>Aylık Dağılım</value>
    <comment>Monthly breakdown section title</comment>
  </data>
  <data name="Monthly Distribution Summary" xml:space="preserve">
    <value>Aylık Dağıtım Özeti</value>
    <comment>Title for monthly distribution summary table</comment>
  </data>
  <data name="Months" xml:space="preserve">
    <value>Ay</value>
    <comment>Months label</comment>
  </data>
  <data name="More Info" xml:space="preserve">
    <value>Detaylı Bilgi</value>
    <comment>More info link text</comment>
  </data>
  <data name="more users with your current package." xml:space="preserve">
    <value>daha fazla kullanıcıyı mevcut paketinizle.</value>
    <comment>Part of message showing remaining invites</comment>
  </data>
  <data name="Multiple ways to grow your digital assets with RazeWin" xml:space="preserve">
    <value>RazeWin ile dijital varlıklarınızı büyütmenin birden çok yolu</value>
    <comment>Earn money section description</comment>
  </data>
  <data name="My Account" xml:space="preserve">
    <value>Hesabım</value>
    <comment>Label for my account section</comment>
  </data>
  <data name="My Balances" xml:space="preserve">
    <value>Bakiyelerim</value>
    <comment>Header for user balances section</comment>
  </data>
  <data name="My Packages" xml:space="preserve">
    <value>Paketlerim</value>
    <comment>Navigation menu item</comment>
  </data>
  <data name="My Payment" xml:space="preserve">
    <value>Ödemelerim</value>
    <comment>My Payment page title</comment>
  </data>
  <data name="My Profile" xml:space="preserve">
    <value>Profilim</value>
    <comment>My Profile page title</comment>
  </data>
  <data name="My Referral Code" xml:space="preserve">
    <value>Referans Kodum</value>
    <comment>Title for user's personal referral code section</comment>
  </data>
  <data name="My Saving Accounts" xml:space="preserve">
    <value>Vadeli Hesaplarım</value>
    <comment>Button: My Saving Accounts (Vadeli Hesaplarım)</comment>
  </data>
  <data name="My Savings" xml:space="preserve">
    <value>Tasarruflarım</value>
    <comment>My savings page title</comment>
  </data>
  <data name="My Trade History" xml:space="preserve">
    <value>İşlem Geçmişim</value>
    <comment>My Trade History page title</comment>
  </data>
  <data name="My Wallet" xml:space="preserve">
    <value>Cüzdanım</value>
    <comment>My Wallet page title</comment>
  </data>
  <data name="My Withdrawals" xml:space="preserve">
    <value>Çekimlerim</value>
    <comment>My Withdrawals page title</comment>
  </data>
  <data name="N/A" xml:space="preserve">
    <value>Uygulanamaz</value>
  </data>
  <data name="Name" xml:space="preserve">
    <value>İsim</value>
    <comment>Label for name field</comment>
  </data>
  <data name="New" xml:space="preserve">
    <value>Yeni</value>
    <comment>Label for new items</comment>
  </data>
  <data name="New Announcement" xml:space="preserve">
    <value>Yeni Duyuru</value>
    <comment>Create new announcement button/title</comment>
  </data>
  <data name="New Balance" xml:space="preserve">
    <value>Yeni Bakiye</value>
    <comment>New balance label</comment>
  </data>
  <data name="New Balance Transaction" xml:space="preserve">
    <value>Yeni Bakiye İşlemi</value>
    <comment>Title for creating a new balance transaction</comment>
  </data>
  <data name="New Password" xml:space="preserve">
    <value>Yeni Şifre</value>
    <comment>New password field label</comment>
  </data>
  <data name="New Wallet Balance" xml:space="preserve">
    <value>Yeni Cüzdan Bakiyesi</value>
    <comment>New wallet balance label</comment>
  </data>
  <data name="Next" xml:space="preserve">
    <value>Sonraki</value>
    <comment>Next page button text</comment>
  </data>
  <data name="Next Interest Payment" xml:space="preserve">
    <value>Sonraki Faiz Ödemesi</value>
    <comment>Label for next interest payment date</comment>
  </data>
  <data name="No" xml:space="preserve">
    <value>Hayır</value>
    <comment>No text</comment>
  </data>
  <data name="No additional data" xml:space="preserve">
    <value>Ek veri yok</value>
    <comment>Default text for empty additional data</comment>
  </data>
  <data name="No Announcements Found" xml:space="preserve">
    <value>Duyuru bulunamadı</value>
    <comment>Message when no announcements exist</comment>
  </data>
  <data name="No available balance for new savings" xml:space="preserve">
    <value>Yeni tasarruf hesabı için kullanılabilir bakiye yok</value>
    <comment>Message when user has no available balance</comment>
  </data>
  <data name="No background services found" xml:space="preserve">
    <value>Arka plan hizmeti bulunamadı</value>
    <comment>Message when no background services are found</comment>
  </data>
  <data name="No data available in table" xml:space="preserve">
    <value>Kayıt yok</value>
    <comment>DataTables empty table text</comment>
  </data>
  <data name="No entries available" xml:space="preserve">
    <value>Kayıt yok</value>
    <comment>DataTable empty info text</comment>
  </data>
  <data name="No Expiry" xml:space="preserve">
    <value>Son Kullanma Tarihi Yok</value>
    <comment>Label for no expiry</comment>
  </data>
  <data name="No hidden fees" xml:space="preserve">
    <value>Gizli ücret yok</value>
    <comment>Benefit 5</comment>
  </data>
  <data name="No inactive savings accounts found." xml:space="preserve">
    <value>Pasif tasarruf hesabı bulunamadı.</value>
    <comment>Message when no inactive accounts exist</comment>
  </data>
  <data name="No interest payments found" xml:space="preserve">
    <value>Faiz ödemesi bulunamadı</value>
    <comment>Message when no payments found</comment>
  </data>
  <data name="No interest payments yet" xml:space="preserve">
    <value>Henüz faiz ödemesi yok</value>
    <comment>Message when no interest payments exist</comment>
  </data>
  <data name="No interest payments yet. Interest will be paid daily starting from tomorrow." xml:space="preserve">
    <value>Henüz faiz ödemesi yok. Faiz yarından itibaren günlük olarak ödenecek.</value>
    <comment>Message when no interest payments exist</comment>
  </data>
  <data name="No limits, no waiting at Razewin! Experience the difference with jet-speed deposit and withdrawal operations, including weekends." xml:space="preserve">
    <value>Razewin'de sınır yok, beklemek yok! Jet hızında haftasonu dahil yatırım ve çekim işlemleri ile farkı yaşayın.</value>
    <comment>Fast withdrawals slider description</comment>
  </data>
  <data name="No matching records found" xml:space="preserve">
    <value>Eşleşen kayıt bulunamadı</value>
    <comment>No matching records found message</comment>
  </data>
  <data name="No Package" xml:space="preserve">
    <value>Paket Yok</value>
    <comment>Used when user has no active package</comment>
  </data>
  <data name="No packages have been purchased after this deposit was approved" xml:space="preserve">
    <value>Bu para yatırma işlemi onaylandıktan sonra hiç paket satın alınmamış</value>
    <comment>Status message for package purchase check</comment>
  </data>
  <data name="No pairs found" xml:space="preserve">
    <value>Çift bulunamadı</value>
    <comment>Message when no trading pairs are found</comment>
  </data>
  <data name="No records available" xml:space="preserve">
    <value>Kayıt yok</value>
    <comment>No records available message</comment>
  </data>
  <data name="No records found" xml:space="preserve">
    <value>Eşleşen kayıt bulunamadı</value>
    <comment>DataTables zero records text</comment>
  </data>
  <data name="No results found" xml:space="preserve">
    <value>Sonuç bulunamadı</value>
    <comment>Text shown when no results are found in Select2 dropdown</comment>
  </data>
  <data name="No Rewards" xml:space="preserve">
    <value>Ödül Yok</value>
  </data>
  <data name="No Rewards Distributed" xml:space="preserve">
    <value>Dağıtılan Ödül Yok</value>
  </data>
  <data name="No rewards have been distributed yet" xml:space="preserve">
    <value>Henüz ödül dağıtılmamış</value>
    <comment>Status message for reward distribution check</comment>
  </data>
  <data name="No Rewards to Distribute" xml:space="preserve">
    <value>Dağıtılacak Ödül Yok</value>
    <comment>Title for the message when there are no rewards to distribute</comment>
  </data>
  <data name="No rewards were distributed for this payment. This could be because:" xml:space="preserve">
    <value>Bu ödeme için hiçbir ödül dağıtılmadı. Bunun nedeni şunlar olabilir:</value>
  </data>
  <data name="No rewards were distributed. There may be no eligible users in the referral chain." xml:space="preserve">
    <value>Hiçbir ödül dağıtılmadı. Referans zincirinde uygun kullanıcı olmayabilir.</value>
  </data>
  <data name="No trades found" xml:space="preserve">
    <value>İşlem bulunamadı</value>
    <comment>Message when no trades are found</comment>
  </data>
  <data name="No trades have been made after this deposit was approved" xml:space="preserve">
    <value>Bu para yatırma işlemi onaylandıktan sonra hiç ticaret yapılmamış</value>
    <comment>Status message for trade activity check</comment>
  </data>
  <data name="No transactions found" xml:space="preserve">
    <value>İşlem bulunamadı</value>
    <comment>Message when no transactions are found</comment>
  </data>
  <data name="No wallet balances found" xml:space="preserve">
    <value>Cüzdan bakiyesi bulunamadı</value>
    <comment>Message when user has no wallet balances</comment>
  </data>
  <data name="No withdrawals have been made after this deposit was approved" xml:space="preserve">
    <value>Bu para yatırma işlemi onaylandıktan sonra hiç para çekme işlemi yapılmamış</value>
    <comment>Status message for withdrawal activity check</comment>
  </data>
  <data name="None" xml:space="preserve">
    <value>Yok</value>
    <comment>Used in referral tree page</comment>
  </data>
  <data name="Not Available" xml:space="preserve">
    <value>Mevcut Değil</value>
    <comment>Text shown when a package is not available for purchase (e.g., lower than current package)</comment>
  </data>
  <data name="Not Eligible for Reversal" xml:space="preserve">
    <value>Geri Almaya Uygun Değil</value>
    <comment>Status text for deposits not eligible for reversal</comment>
  </data>
  <data name="Not specified" xml:space="preserve">
    <value>Belirtilmemiş</value>
    <comment>Default text for unspecified values</comment>
  </data>
  <data name="Not Started" xml:space="preserve">
    <value>Başlatılmadı</value>
    <comment>Not started status</comment>
  </data>
  <data name="Not Verified" xml:space="preserve">
    <value>Doğrulanmadı</value>
    <comment>Verification status - not verified</comment>
  </data>
  <data name="Note When you approve a cryptocurrency deposit the amount will be added to the users cryptocurrency wallet automatically" xml:space="preserve">
    <value>Not: Bir kripto para yat─▒rma i┼ƒlemini onaylad─▒─ƒ─▒n─▒zda, tutar otomatik olarak kullan─▒c─▒n─▒n kripto para c├╝zdan─▒na eklenecektir.</value>
    <comment>Note for admin when approving cryptocurrency deposits</comment>
  </data>
  <data name="Note When you approve a payment the amount will be added to the users balance automatically" xml:space="preserve">
    <value>Note: When you approve a payment, the amount will be added to the user's balance automatically.</value>
    <comment>Note for admin when approving payments</comment>
  </data>
  <data name="Note When you approve a withdrawal the amount will be deducted from the users balance automatically" xml:space="preserve">
    <value>Note: When you approve a withdrawal, the amount will be deducted from the user's balance automatically.</value>
    <comment>Note for admin when approving withdrawals</comment>
  </data>
  <data name="Note: When you approve a cryptocurrency deposit, the amount will be added to the user's cryptocurrency wallet automatically." xml:space="preserve">
    <value>Not: Bir kripto para yatırma işlemini onayladığınızda, tutar otomatik olarak kullanıcının kripto para cüzdanına eklenecektir.</value>
    <comment>Note for admin when approving cryptocurrency deposits</comment>
  </data>
  <data name="Note: When you approve a payment, the amount will be added to the user's balance automatically." xml:space="preserve">
    <value>Not: Bir ödemeyi onayladığınızda, tutar otomatik olarak kullanıcının bakiyesine eklenecektir.</value>
    <comment>Note for admin when approving payments</comment>
  </data>
  <data name="Note: When you approve a withdrawal, the amount will be deducted from the user's balance automatically." xml:space="preserve">
    <value>Not: Bir çekimi onayladığınızda, tutar otomatik olarak kullanıcının bakiyesinden düşülecektir.</value>
    <comment>Note for admin when approving withdrawals</comment>
  </data>
  <data name="of" xml:space="preserve">
    <value>/</value>
    <comment>Of preposition for pagination</comment>
  </data>
  <data name="OK" xml:space="preserve">
    <value>Tamam</value>
    <comment>OK button text</comment>
  </data>
  <data name="ON JUNE 25TH" xml:space="preserve">
    <value>AÇIKLANIYOR!</value>
    <comment>Date specification for RZW Token slider</comment>
  </data>
  <data name="Only approved payments can have rewards distributed." xml:space="preserve">
    <value>Yalnızca onaylanmış ödemeler için ödüller dağıtılabilir.</value>
  </data>
  <data name="Only Digits For Decimal Part" xml:space="preserve">
    <value>Lütfen ondalık kısım için sadece rakam giriniz</value>
    <comment>Validation message for decimal part</comment>
  </data>
  <data name="Only send Bitcoin (BTC) to this address. Sending any other cryptocurrency may result in permanent loss." xml:space="preserve">
    <value>Bu adrese sadece Bitcoin (BTC) gönderin. Başka bir kripto para göndermeniz kalıcı kayıplara neden olabilir.</value>
    <comment>Warning for Bitcoin deposit address</comment>
  </data>
  <data name="Only send BNB on the BEP20 (Smart Chain) network to this address. Sending BNB on other networks or other cryptocurrencies may result in permanent loss." xml:space="preserve">
    <value>Bu adrese sadece BEP20 (Smart Chain) ağı üzerinden BNB gönderin. Diğer ağlar üzerinden BNB veya başka kripto paralar göndermeniz kalıcı kayıplara neden olabilir.</value>
    <comment>Warning for BNB deposit address</comment>
  </data>
  <data name="Only send USDT on the TRC20 network to this address. Sending USDT on other networks or other cryptocurrencies may result in permanent loss." xml:space="preserve">
    <value>Bu adrese sadece TRC20 ağı üzerinden USDT gönderin. Diğer ağlar üzerinden USDT veya başka kripto paralar göndermeniz kalıcı kayıplara neden olabilir.</value>
    <comment>Warning for USDT deposit address</comment>
  </data>
  <data name="Only your most recently purchased package will remain active" xml:space="preserve">
    <value>Sadece en son satın aldığınız paket aktif kalacaktır</value>
    <comment>Message informing users about active package policy</comment>
  </data>
  <data name="Optional" xml:space="preserve">
    <value>İsteğe Bağlı</value>
    <comment>Label for optional fields</comment>
  </data>
  <data name="Order" xml:space="preserve">
    <value>Sıra</value>
    <comment>Label for order field</comment>
  </data>
  <data name="Other" xml:space="preserve">
    <value>Diğer</value>
    <comment>Transaction type for other transactions</comment>
  </data>
  <data name="Our Company" xml:space="preserve">
    <value>Şirketimiz</value>
    <comment>Company tab title</comment>
  </data>
  <data name="Our Mission &amp; Vision" xml:space="preserve">
    <value>Misyonumuz &amp; Vizyonumuz</value>
    <comment>Label for mission and vision section</comment>
  </data>
  <data name="Our Price Providers" xml:space="preserve">
    <value>Fiyat Sağlayıcılarımız</value>
    <comment>Price providers page title</comment>
  </data>
  <data name="Our Team" xml:space="preserve">
    <value>Ekibimiz</value>
    <comment>Team tab title</comment>
  </data>
  <data name="Package" xml:space="preserve">
    <value>Paket</value>
    <comment>Label for package</comment>
  </data>
  <data name="Package Bonus" xml:space="preserve">
    <value>Paket Bonusu</value>
    <comment>Label for package bonus trade type</comment>
  </data>
  <data name="package for" xml:space="preserve">
    <value>paketi için</value>
    <comment>Confirmation message part 2</comment>
  </data>
  <data name="Package Purchase" xml:space="preserve">
    <value>Paket Satın Alımı</value>
    <comment>Payment type for package purchases</comment>
  </data>
  <data name="Package Purchase Check" xml:space="preserve">
    <value>Paket Satın Alma Kontrolü</value>
    <comment>Check item for package purchase</comment>
  </data>
  <data name="Package purchased successfully" xml:space="preserve">
    <value>Paket başarıyla satın alındı</value>
    <comment>Success message after purchase</comment>
  </data>
  <data name="Package purchased successfully. You have received {0} RZW tokens." xml:space="preserve">
    <value>Package purchased successfully. You have received {0} RZW tokens.</value>
    <comment>Success message after package purchase with RZW tokens</comment>
  </data>
  <data name="Package Reward Percentage" xml:space="preserve">
    <value>Paket Ödül Yüzdesi</value>
    <comment>Label for package reward percentage</comment>
  </data>
  <data name="Package Reward Percentages" xml:space="preserve">
    <value>Paket Ödül Yüzdeleri</value>
    <comment>Title for package reward percentages page</comment>
  </data>
  <data name="Packages" xml:space="preserve">
    <value>Paketler</value>
    <comment>Navigation menu item for packages</comment>
  </data>
  <data name="Page" xml:space="preserve">
    <value>Sayfa</value>
    <comment>Page label for pagination</comment>
  </data>
  <data name="Paid" xml:space="preserve">
    <value>Ödendi</value>
    <comment>Payment status - paid</comment>
  </data>
  <data name="Pair Code" xml:space="preserve">
    <value>Parite Kodu</value>
    <comment>Market pair code (e.g. BTCTRY)</comment>
  </data>
  <data name="Papara" xml:space="preserve">
    <value>Papara</value>
    <comment>Label for Papara payment method</comment>
  </data>
  <data name="Papara No" xml:space="preserve">
    <value>Papara No</value>
    <comment>Papara number field label</comment>
  </data>
  <data name="Papara Number Required" xml:space="preserve">
    <value>Papara numarası gereklidir</value>
    <comment>Papara number validation message</comment>
  </data>
  <data name="Passive" xml:space="preserve">
    <value>Pasif</value>
    <comment>Label for passive status</comment>
  </data>
  <data name="Password" xml:space="preserve">
    <value>Şifre</value>
    <comment>Password field label</comment>
  </data>
  <data name="Password changed successfully" xml:space="preserve">
    <value>Şifre başarıyla değiştirildi</value>
    <comment>Success message for password change</comment>
  </data>
  <data name="Payment Details" xml:space="preserve">
    <value>Ödeme Detayları</value>
  </data>
  <data name="Payment History" xml:space="preserve">
    <value>Ödeme Geçmişi</value>
    <comment>Label for payment history section</comment>
  </data>
  <data name="Payment ID" xml:space="preserve">
    <value>Ödeme ID</value>
  </data>
  <data name="Payment Information" xml:space="preserve">
    <value>Ödeme Bilgileri</value>
    <comment>Payment information section title</comment>
  </data>
  <data name="Payment Instructions" xml:space="preserve">
    <value>Ödeme Talimatları</value>
    <comment>Label for payment instructions section</comment>
  </data>
  <data name="Payment Methods" xml:space="preserve">
    <value>Ödeme Yöntemleri</value>
    <comment>Payment methods section title</comment>
  </data>
  <data name="Payment request submitted successfully. Your account will be credited once the payment is verified." xml:space="preserve">
    <value>Ödeme talebi başarıyla gönderildi. Ödeme doğrulandıktan sonra hesabınıza yansıtılacaktır.</value>
    <comment>Success message for payment submission</comment>
  </data>
  <data name="Payment Reward Summary" xml:space="preserve">
    <value>Ödeme Ödül Özeti</value>
  </data>
  <data name="Payment Type" xml:space="preserve">
    <value>Ödeme Türü</value>
    <comment>Payment type label</comment>
  </data>
  <data name="Payments" xml:space="preserve">
    <value>Ödemeler</value>
    <comment>Payments menu item</comment>
  </data>
  <data name="Pending" xml:space="preserve">
    <value>Beklemede</value>
    <comment>Pending status</comment>
  </data>
  <data name="Pending Interest Payments" xml:space="preserve">
    <value>Bekleyen Faiz Ödemeleri</value>
    <comment>Label for pending interest payments</comment>
  </data>
  <data name="Pending Orders" xml:space="preserve">
    <value>Bekleyen Emirler</value>
    <comment>Label for pending orders section</comment>
  </data>
  <data name="Pending Payments" xml:space="preserve">
    <value>Bekleyen Ödemeler</value>
    <comment>Pending payments count on dashboard</comment>
  </data>
  <data name="Pending Withdrawals" xml:space="preserve">
    <value>Bekleyen Para Çekimleri</value>
    <comment>Pending withdrawals count on dashboard</comment>
  </data>
  <data name="Percentage" xml:space="preserve">
    <value>Yüzde</value>
    <comment>Label for percentage</comment>
  </data>
  <data name="Performance Metrics" xml:space="preserve">
    <value>Performans Metrikleri</value>
    <comment>Section title for performance metrics</comment>
  </data>
  <data name="Period" xml:space="preserve">
    <value>Dönem</value>
    <comment>Column header for period range</comment>
  </data>
  <data name="Phone" xml:space="preserve">
    <value>Telefon</value>
    <comment>Phone label</comment>
  </data>
  <data name="Phone Number" xml:space="preserve">
    <value>Telefon Numarası</value>
    <comment>Phone number label</comment>
  </data>
  <data name="Phone Number Required" xml:space="preserve">
    <value>Telefon numarası gereklidir</value>
    <comment>Phone number validation message</comment>
  </data>
  <data name="Phone Required" xml:space="preserve">
    <value>Telefon gereklidir</value>
    <comment>Validation message for phone</comment>
  </data>
  <data name="Plan created successfully" xml:space="preserve">
    <value>Plan başarıyla oluşturuldu</value>
    <comment>Success message for plan creation</comment>
  </data>
  <data name="Plan deleted successfully" xml:space="preserve">
    <value>Plan başarıyla silindi</value>
    <comment>Success message for plan deletion</comment>
  </data>
  <data name="Plan Details" xml:space="preserve">
    <value>Plan Detayları</value>
    <comment>Section title for plan details</comment>
  </data>
  <data name="Plan Management" xml:space="preserve">
    <value>Plan Yönetimi</value>
    <comment>Admin page title for plan management</comment>
  </data>
  <data name="Plan Name" xml:space="preserve">
    <value>Plan Adı</value>
    <comment>Savings plan name</comment>
  </data>
  <data name="Plan Performance" xml:space="preserve">
    <value>Plan Performansı</value>
    <comment>Page title for plan performance report</comment>
  </data>
  <data name="Plan Performance Report" xml:space="preserve">
    <value>Plan Performans Raporu</value>
    <comment>Report section title</comment>
  </data>
  <data name="Plan Statistics" xml:space="preserve">
    <value>Plan İstatistikleri</value>
    <comment>Section title for plan statistics</comment>
  </data>
  <data name="Plan updated successfully" xml:space="preserve">
    <value>Plan başarıyla güncellendi</value>
    <comment>Success message for plan update</comment>
  </data>
  <data name="Plans Loading" xml:space="preserve">
    <value>Planlar Yükleniyor</value>
    <comment>Loading message for plans</comment>
  </data>
  <data name="Please Do Not Click The Completed Deposit Button Before Making The Deposit" xml:space="preserve">
    <value>Lütfen yatırım yapmadan "Yatırımı Gerçekleştirdim" butonuna tıklamayınız</value>
    <comment>Warning message for deposit pages</comment>
  </data>
  <data name="Please ensure that the bank account details are correct. We are not responsible for transfers to incorrect accounts." xml:space="preserve">
    <value>Lütfen banka hesap bilgilerinin doğru olduğundan emin olun. Yanlış hesaplara yapılan transferlerden sorumlu değiliz.</value>
    <comment>Bank account warning message</comment>
  </data>
  <data name="Please enter a valid amount" xml:space="preserve">
    <value>Lütfen geçerli bir tutar giriniz</value>
    <comment>Validation message for amount field</comment>
  </data>
  <data name="Please enter a valid format" xml:space="preserve">
    <value>Lütfen geçerli bir format giriniz</value>
    <comment>Generic validation message for pattern validation</comment>
  </data>
  <data name="Please enter a valid IBAN" xml:space="preserve">
    <value>Lütfen geçerli bir IBAN giriniz</value>
    <comment>Validation message for IBAN format</comment>
  </data>
  <data name="Please enter a valid number" xml:space="preserve">
    <value>Lütfen geçerli bir sayı giriniz</value>
    <comment>Validation message for numeric fields</comment>
  </data>
  <data name="Please enter a valid number with comma as decimal separator (e.g., 123,45)" xml:space="preserve">
    <value>Lütfen ondalık ayırıcı olarak virgül kullanarak geçerli bir sayı girin (örn. 123,45)</value>
    <comment>Validation message for number fields</comment>
  </data>
  <data name="Please enter account holder name" xml:space="preserve">
    <value>Lütfen hesap sahibinin adını giriniz</value>
    <comment>Validation message for account holder field</comment>
  </data>
  <data name="Please enter IBAN" xml:space="preserve">
    <value>Lütfen IBAN giriniz</value>
    <comment>Validation message for IBAN field</comment>
  </data>
  <data name="Please enter only digits for decimal part" xml:space="preserve">
    <value>Lütfen ondalık kısım için sadece rakam giriniz</value>
    <comment>Validation message for decimal part</comment>
  </data>
  <data name="Please enter the reference number or description of your bank transfer." xml:space="preserve">
    <value>Lütfen banka transferinizin referans numarasını veya açıklamasını girin.</value>
    <comment>Instruction for bank transfer reference field</comment>
  </data>
  <data name="Please Enter Valid Amount" xml:space="preserve">
    <value>Lütfen geçerli bir tutar giriniz</value>
    <comment>Validation message for invalid amount</comment>
  </data>
  <data name="Please login to make Buy/Sell transactions" xml:space="preserve">
    <value>Alım/Satım yapabilmek için lütfen giriş yapınız.</value>
    <comment>Message shown when unauthenticated user tries to buy/sell</comment>
  </data>
  <data name="Please make sure to include your email address or user ID in the transaction description to help us identify your payment." xml:space="preserve">
    <value>Ödemenizi tanımlamamıza yardımcı olmak için lütfen işlem açıklamasına e-posta adresinizi veya kullanıcı kimliğinizi eklediğinizden emin olun.</value>
    <comment>Instruction for payment identification</comment>
  </data>
  <data name="Please make sure to send your cryptocurrency to the correct network address. Sending to the wrong network may result in permanent loss of funds." xml:space="preserve">
    <value>Lütfen kripto paranızı doğru ağ adresine gönderdiğinizden emin olun. Yanlış ağa gönderim, fonlarınızın kalıcı olarak kaybedilmesine neden olabilir.</value>
    <comment>Warning for cryptocurrency deposits</comment>
  </data>
  <data name="Please select a plan to see amount limits" xml:space="preserve">
    <value>Miktar limitlerini görmek için bir plan seçin</value>
    <comment>Helper text for amount input</comment>
  </data>
  <data name="Please select a user" xml:space="preserve">
    <value>Lütfen bir kullanıcı seçin</value>
    <comment>Validation message for user selection</comment>
  </data>
  <data name="Please select a valid plan" xml:space="preserve">
    <value>Lütfen geçerli bir plan seçin</value>
    <comment>Validation message for plan selection</comment>
  </data>
  <data name="Please Select Bank" xml:space="preserve">
    <value>Lütfen banka seçiniz</value>
    <comment>Bank selection placeholder</comment>
  </data>
  <data name="Please select date range and click Generate Report to view RZW distribution data." xml:space="preserve">
    <value>RZW dağıtım verilerini görüntülemek için tarih aralığını seçin ve Rapor Oluştur'a tıklayın.</value>
    <comment>Information message for empty report state</comment>
  </data>
  <data name="Please wait" xml:space="preserve">
    <value>Lütfen bekleyin</value>
    <comment>Please wait message</comment>
  </data>
  <data name="Popular" xml:space="preserve">
    <value>Popüler</value>
    <comment>Label for popular package</comment>
  </data>
  <data name="Previous" xml:space="preserve">
    <value>Önceki</value>
    <comment>Previous page button text</comment>
  </data>
  <data name="Previous Balance" xml:space="preserve">
    <value>Önceki Bakiye</value>
    <comment>Previous balance column header</comment>
  </data>
  <data name="Previous package deactivated" xml:space="preserve">
    <value>Önceki paket devre dışı bırakıldı</value>
    <comment>Message shown when a previous package is deactivated</comment>
  </data>
  <data name="Previous Wallet Balance" xml:space="preserve">
    <value>Önceki Cüzdan Bakiyesi</value>
    <comment>Previous wallet balance label</comment>
  </data>
  <data name="Price" xml:space="preserve">
    <value>Fiyat</value>
    <comment>Price label</comment>
  </data>
  <data name="Principal Amount" xml:space="preserve">
    <value>Ana Para</value>
    <comment>Label for principal amount in interest history</comment>
  </data>
  <data name="Privacy Acceptance Text" xml:space="preserve">
    <value>Bu gizlilik sözleşmesini kabul ederek, kişisel verilerinizin burada belirtilen şekilde işlenmesine onay vermiş olursunuz.</value>
    <comment>Text for privacy acceptance in privacy agreement</comment>
  </data>
  <data name="Privacy Agreement" xml:space="preserve">
    <value>Gizlilik Sözleşmesi</value>
    <comment>Label for privacy agreement</comment>
  </data>
  <data name="Privacy Agreement Read" xml:space="preserve">
    <value>Gizlilik Sözleşmesini Okudum</value>
    <comment>Privacy agreement checkbox label</comment>
  </data>
  <data name="Privacy Agreement Title" xml:space="preserve">
    <value>RAZEWIN Gizlilik Sözleşmesi</value>
    <comment>Title for the privacy agreement</comment>
  </data>
  <data name="Privacy Policy" xml:space="preserve">
    <value>Gizlilik Politikası</value>
    <comment>Privacy Policy page title</comment>
  </data>
  <data name="Process Date" xml:space="preserve">
    <value>İşlem Tarihi</value>
  </data>
  <data name="Process Referral Rewards" xml:space="preserve">
    <value>Referans Ödüllerini İşle</value>
    <comment>Label for process referral rewards</comment>
  </data>
  <data name="Process Status" xml:space="preserve">
    <value>İşlem Durumu</value>
    <comment>Process status column header</comment>
  </data>
  <data name="Processed" xml:space="preserve">
    <value>İşlendi</value>
  </data>
  <data name="Product Details" xml:space="preserve">
    <value>Ürün Detayları</value>
    <comment>Product Details</comment>
  </data>
  <data name="Products" xml:space="preserve">
    <value>Ürünler</value>
    <comment>Products menu item</comment>
  </data>
  <data name="Profile" xml:space="preserve">
    <value>Profil</value>
    <comment>Profile tab title</comment>
  </data>
  <data name="Profile updated successfully" xml:space="preserve">
    <value>Profil başarıyla güncellendi</value>
    <comment>Success message for profile update</comment>
  </data>
  <data name="Progress" xml:space="preserve">
    <value>İlerleme</value>
    <comment>Progress label</comment>
  </data>
  <data name="Projected Final Amount" xml:space="preserve">
    <value>Tahmini Son Miktar</value>
    <comment>Label for projected final amount</comment>
  </data>
  <data name="Projected Maturity Amount" xml:space="preserve">
    <value>Vade Sonunda Toplam Miktar</value>
    <comment>Total amount (principal + interest) at maturity</comment>
  </data>
  <data name="Projected Monthly Earnings" xml:space="preserve">
    <value>Tahmini Aylık Kazanç</value>
    <comment>Projected monthly earnings label</comment>
  </data>
  <data name="Projected Total Interest" xml:space="preserve">
    <value>Vade Sonunda Kazanılacak Toplam Faiz</value>
    <comment>Total interest that will be earned at maturity</comment>
  </data>
  <data name="Projects" xml:space="preserve">
    <value>Projeler</value>
    <comment>Projects menu item</comment>
  </data>
  <data name="Promotions" xml:space="preserve">
    <value>Promosyonlar</value>
    <comment>Promotions page title</comment>
  </data>
  <data name="Provide your bank account details." xml:space="preserve">
    <value>Banka hesap bilgilerinizi girin.</value>
    <comment>Withdrawal step 2</comment>
  </data>
  <data name="Publish Announcement" xml:space="preserve">
    <value>Duyuru Yayınla</value>
    <comment>Publish announcement button</comment>
  </data>
  <data name="Published" xml:space="preserve">
    <value>Yayınlandı</value>
    <comment>Published status</comment>
  </data>
  <data name="Purchase" xml:space="preserve">
    <value>Alış</value>
    <comment>Purchase noun for trade history</comment>
  </data>
  <data name="Purchase a package to start earning referral rewards." xml:space="preserve">
    <value>Referans ödülleri kazanmaya başlamak için bir paket satın alın.</value>
    <comment>Prompt to purchase package</comment>
  </data>
  <data name="Purchase Date" xml:space="preserve">
    <value>Satın Alma Tarihi</value>
    <comment>Label for purchase date</comment>
  </data>
  <data name="Purchase Now" xml:space="preserve">
    <value>Şimdi Satın Al</value>
    <comment>Button text for package purchase</comment>
  </data>
  <data name="Purchase of {0} package" xml:space="preserve">
    <value>{0} paketi satın alımı</value>
    <comment>Description for package purchase transaction</comment>
  </data>
  <data name="Purchase of {0} package by admin" xml:space="preserve">
    <value>Admin tarafından {0} paketi satın alımı</value>
    <comment>Description for package purchase transaction by admin</comment>
  </data>
  <data name="Quick Links" xml:space="preserve">
    <value>Hızlı Bağlantılar</value>
    <comment>Quick links section title</comment>
  </data>
  <data name="Quick Stats" xml:space="preserve">
    <value>Hızlı İstatistikler</value>
    <comment>Label for quick statistics section</comment>
  </data>
  <data name="Rate" xml:space="preserve">
    <value>Oran</value>
    <comment>Rate column header</comment>
  </data>
  <data name="Rate (TRY)" xml:space="preserve">
    <value>Kur (TRY)</value>
    <comment>Label for TRY rate field</comment>
  </data>
  <data name="RazeWin Fast Withdrawals" xml:space="preserve">
    <value>RAZEWIN HIZLI ÇEKİMLER</value>
    <comment>Fast withdrawals title</comment>
  </data>
  <data name="RAZEWIN TRAVEL EARNING APP" xml:space="preserve">
    <value>RAZEWIN SEYAHAT KAZANÇ UYGULAMASI</value>
    <comment>First slider title</comment>
  </data>
  <data name="Recalculate Rewards" xml:space="preserve">
    <value>Ödülleri Yeniden Hesapla</value>
  </data>
  <data name="Recent Trades" xml:space="preserve">
    <value>Son İşlemler</value>
    <comment>Recent trades section title</comment>
  </data>
  <data name="Recipient" xml:space="preserve">
    <value>Alıcı</value>
    <comment>Recipient label</comment>
  </data>
  <data name="Record Balance Transaction" xml:space="preserve">
    <value>Bakiye İşlemini Kaydet</value>
    <comment>Label for checkbox to record balance transaction</comment>
  </data>
  <data name="Reference ID" xml:space="preserve">
    <value>Referans ID</value>
    <comment>Label for reference ID field</comment>
  </data>
  <data name="Reference Type" xml:space="preserve">
    <value>Referans Türü</value>
    <comment>Label for reference type field</comment>
  </data>
  <data name="References" xml:space="preserve">
    <value>Referanslar</value>
    <comment>Label for references section</comment>
  </data>
  <data name="Referral" xml:space="preserve">
    <value>Referans</value>
    <comment>Tab title for referral section</comment>
  </data>
  <data name="Referral Code" xml:space="preserve">
    <value>Referans Kodu</value>
    <comment>Code used for referring new users</comment>
  </data>
  <data name="Referral code copied to clipboard" xml:space="preserve">
    <value>Referans kodu panoya kopyalandı</value>
    <comment>Success message when referral code is copied</comment>
  </data>
  <data name="Referral Deposit Reward Percentages" xml:space="preserve">
    <value>Referans Seviyesine Göre Para Yatırma Ödülü %</value>
    <comment>Text explaining that the percentages shown are rewards earned from deposits made by referred users at different levels</comment>
  </data>
  <data name="Referral Deposit Rewards" xml:space="preserve">
    <value>Yatırım Yönlendirme Ödülleri</value>
    <comment>Title for referral deposit rewards section</comment>
  </data>
  <data name="Referral Hierarchy" xml:space="preserve">
    <value>Referans Hiyerarşisi</value>
    <comment>Used in Profile page to show referral tree</comment>
  </data>
  <data name="Referral Link" xml:space="preserve">
    <value>Davet Linki</value>
    <comment>Label for the referral link field</comment>
  </data>
  <data name="Referral Packages" xml:space="preserve">
    <value>Referans Paketleri</value>
    <comment>Used for package display pages</comment>
  </data>
  <data name="Referral Program Text" xml:space="preserve">
    <value>RAZEWIN, kullanıcılarına referans kodu ile yeni kullanıcılar davet etme imkanı sunmaktadır. Referans programı kapsamında kazanılan ödüller, platformun belirlediği şartlara tabidir.</value>
    <comment>Text for referral program section in user agreement</comment>
  </data>
  <data name="Referral Program Title" xml:space="preserve">
    <value>Referans Programı</value>
    <comment>Title for referral program section in user agreement</comment>
  </data>
  <data name="Referral Reward" xml:space="preserve">
    <value>Referans Ödülü</value>
    <comment>Transaction type for referral rewards</comment>
  </data>
  <data name="Referral Reward RZW" xml:space="preserve">
    <value>Referans Ödül RZW</value>
    <comment>Column header for referral reward RZW amount</comment>
  </data>
  <data name="Referral Rewards" xml:space="preserve">
    <value>Referral Ödülleri</value>
    <comment>Text shown in package display for referral reward percentages</comment>
  </data>
  <data name="Referral Rewards Details" xml:space="preserve">
    <value>Referans Ödül Detayları</value>
    <comment>Title for referral rewards details section</comment>
  </data>
  <data name="Referral System" xml:space="preserve">
    <value>Referans Sistemi</value>
    <comment>Navigation menu item for referral system</comment>
  </data>
  <data name="Referral Tree" xml:space="preserve">
    <value>Referans Ağacı</value>
    <comment>Used in referral hierarchy view</comment>
  </data>
  <data name="Referred By" xml:space="preserve">
    <value>Referans Eden</value>
    <comment>Used in referral tree page</comment>
  </data>
  <data name="Referred Users" xml:space="preserve">
    <value>Referans Olan Kullanıcılar</value>
    <comment>Title for list of users referred by current user</comment>
  </data>
  <data name="Refresh Interval" xml:space="preserve">
    <value>Yenileme Aralığı</value>
    <comment>Label for refresh interval dropdown</comment>
  </data>
  <data name="Refresh Now" xml:space="preserve">
    <value>Şimdi Yenile</value>
    <comment>Button text to refresh background service status</comment>
  </data>
  <data name="Register" xml:space="preserve">
    <value>Kayıt Ol</value>
    <comment>Register page title</comment>
  </data>
  <data name="Register a new membership" xml:space="preserve">
    <value>Yeni bir üyelik oluştur</value>
    <comment>Label for registration link</comment>
  </data>
  <data name="Registered Users" xml:space="preserve">
    <value>Kayıtlı Kullanıcı</value>
    <comment>Registered users label</comment>
  </data>
  <data name="Registration Date" xml:space="preserve">
    <value>Kayıt Tarihi</value>
    <comment>Registration date column header</comment>
  </data>
  <data name="Rejected" xml:space="preserve">
    <value>Reddedildi</value>
    <comment>Rejected status</comment>
  </data>
  <data name="Remaining Interest" xml:space="preserve">
    <value>Kalan Faiz</value>
    <comment>Label for remaining interest to be earned</comment>
  </data>
  <data name="Remember Me" xml:space="preserve">
    <value>Beni Hatırla</value>
    <comment>Label for remember me checkbox</comment>
  </data>
  <data name="Report Filters" xml:space="preserve">
    <value>Rapor Filtreleri</value>
    <comment>Title for report filters section</comment>
  </data>
  <data name="Report Type" xml:space="preserve">
    <value>Rapor Türü</value>
    <comment>Label for report type selection</comment>
  </data>
  <data name="Report Your Deposit" xml:space="preserve">
    <value>Yatırımınızı Bildirin</value>
    <comment>Label for deposit reporting section</comment>
  </data>
  <data name="Reports" xml:space="preserve">
    <value>Raporlar</value>
    <comment>Navigation header for reports section</comment>
  </data>
  <data name="Retry" xml:space="preserve">
    <value>Tekrar Dene</value>
  </data>
  <data name="Return on Investment" xml:space="preserve">
    <value>Yatırım Getirisi</value>
    <comment>Label for return on investment percentage</comment>
  </data>
  <data name="Reversal Eligibility Check" xml:space="preserve">
    <value>Geri Alma Uygunluk Kontrolü</value>
    <comment>Title for reversal eligibility check section</comment>
  </data>
  <data name="Reverse Deposit" xml:space="preserve">
    <value>Para Yatırma İşlemini Geri Al</value>
    <comment>Button and page title for reversing a deposit</comment>
  </data>
  <data name="Reward Distribution" xml:space="preserve">
    <value>Ödül Dağıtımı</value>
  </data>
  <data name="Reward distribution cannot be undone. Make sure this payment is legitimate before proceeding." xml:space="preserve">
    <value>Ödül dağıtımı geri alınamaz. Devam etmeden önce bu ödemenin meşru olduğundan emin olun.</value>
  </data>
  <data name="Reward Distribution Check" xml:space="preserve">
    <value>Ödül Dağıtımı Kontrolü</value>
    <comment>Check item for reward distribution</comment>
  </data>
  <data name="Reward Distribution Information" xml:space="preserve">
    <value>Ödül Dağıtım Bilgileri</value>
  </data>
  <data name="Reward Percentages" xml:space="preserve">
    <value>Ödül Yüzdeleri</value>
    <comment>Navigation menu item for reward percentages</comment>
  </data>
  <data name="Reward Preview" xml:space="preserve">
    <value>Ödül Önizlemesi</value>
    <comment>Title for the reward preview section</comment>
  </data>
  <data name="Reward Recalculation" xml:space="preserve">
    <value>Ödül Yeniden Hesaplama</value>
  </data>
  <data name="Reward Rzw Amount" xml:space="preserve">
    <value>RZW Ödül Miktarı</value>
  </data>
  <data name="Reward Rzw Percentage" xml:space="preserve">
    <value>RZW Ödül Yüzdesi</value>
  </data>
  <data name="Reward Status" xml:space="preserve">
    <value>Ödül Durumu</value>
  </data>
  <data name="Reward Summary" xml:space="preserve">
    <value>Ödül Özeti</value>
  </data>
  <data name="Reward Tl Amount" xml:space="preserve">
    <value>TL Ödül Miktarı</value>
  </data>
  <data name="Reward Tl Percentage" xml:space="preserve">
    <value>TL Ödül Yüzdesi</value>
  </data>
  <data name="Rewarded Users Count" xml:space="preserve">
    <value>Ödüllendirilen Kullanıcı Sayısı</value>
  </data>
  <data name="Rewards have already been distributed for this payment." xml:space="preserve">
    <value>Bu ödeme için ödüller zaten dağıtılmış.</value>
  </data>
  <data name="Rewards have been distributed successfully. {0} users received a total of {1} RZW." xml:space="preserve">
    <value>Ödüller başarıyla dağıtıldı. {0} kullanıcı toplam {1} RZW aldı.</value>
  </data>
  <data name="Rewards have been distributed to {0} users" xml:space="preserve">
    <value>Ödüller {0} kullanıcıya dağıtılmış</value>
    <comment>Status message for reward distribution check with count</comment>
  </data>
  <data name="Rich Investment Variety" xml:space="preserve">
    <value>Zengin Yatırım Çeşitliliği</value>
    <comment>Label for investment variety section</comment>
  </data>
  <data name="Risk Disclosure" xml:space="preserve">
    <value>Risk Bildirimi</value>
    <comment>Label for risk disclosure section</comment>
  </data>
  <data name="Running" xml:space="preserve">
    <value>Çalışıyor</value>
    <comment>Running status</comment>
  </data>
  <data name="RZW Amount" xml:space="preserve">
    <value>RZW Miktarı</value>
    <comment>Label for RZW amount input</comment>
  </data>
  <data name="RZW Distribution Breakdown" xml:space="preserve">
    <value>RZW Dağıtım Dökümü</value>
    <comment>Title for distribution breakdown chart</comment>
  </data>
  <data name="RZW Distribution Reports" xml:space="preserve">
    <value>RZW Dağıtım Raporları</value>
    <comment>Title for RZW distribution reports page</comment>
  </data>
  <data name="RZW Price at Distribution" xml:space="preserve">
    <value>Dağıtım Anındaki RZW Fiyatı</value>
  </data>
  <data name="RZW Savings" xml:space="preserve">
    <value>RZW Tasarruf Hesabı</value>
    <comment>RZW Savings title</comment>
  </data>
  <data name="RZW Savings plans define the terms and interest rates for savings accounts." xml:space="preserve">
    <value>RZW Vadeli hesap planları, tasarruf hesapların şartlarını ve faiz oranlarını belirler.</value>
    <comment>Info message about savings plans</comment>
  </data>
  <data name="RZW Savings Reports" xml:space="preserve">
    <value>RZW Tasarruf Hesabı Raporları</value>
    <comment>Page title for reports</comment>
  </data>
  <data name="RZW TOKEN" xml:space="preserve">
    <value>RZW TOKEN</value>
    <comment>RZW Token name for listing slider</comment>
  </data>
  <data name="RZW token not found in the system" xml:space="preserve">
    <value>Sistemde RZW token bulunamadı</value>
    <comment>Error message when RZW token is not found in the database</comment>
  </data>
  <data name="Sale" xml:space="preserve">
    <value>Satış</value>
    <comment>Sale noun for trade history</comment>
  </data>
  <data name="Save" xml:space="preserve">
    <value>Kaydet</value>
    <comment>Label for save button</comment>
  </data>
  <data name="Save Announcement" xml:space="preserve">
    <value>Duyuru Kaydet</value>
    <comment>Save announcement button</comment>
  </data>
  <data name="Save Changes" xml:space="preserve">
    <value>Değişiklikleri Kaydet</value>
    <comment>Label for save changes button</comment>
  </data>
  <data name="Savings" xml:space="preserve">
    <value>Tasarruf</value>
    <comment>Savings title</comment>
  </data>
  <data name="Savings Account" xml:space="preserve">
    <value>Tasarruf Hesabı</value>
    <comment>Savings account filter label</comment>
  </data>
  <data name="Savings Account Closing" xml:space="preserve">
    <value>Tasarruf Hesabı Kapatma</value>
    <comment>Trade type for RZW savings account closing</comment>
  </data>
  <data name="Savings account created successfully! Your RZW tokens have been locked and will start earning interest." xml:space="preserve">
    <value>Tasarruf hesabı başarıyla oluşturuldu! RZW tokenlarınız kilitlendi ve faiz kazanmaya başlayacak.</value>
    <comment>Success message for account creation</comment>
  </data>
  <data name="Savings Account Details" xml:space="preserve">
    <value>Tasarruf Hesabı Detayları</value>
    <comment>Page title for savings account details</comment>
  </data>
  <data name="Savings Account Early Closing" xml:space="preserve">
    <value>Tasarruf Hesabı Erken Kapatma</value>
    <comment>Trade type for RZW savings account early closing</comment>
  </data>
  <data name="Savings account not found or you don't have permission to view it." xml:space="preserve">
    <value>Tasarruf hesabı bulunamadı veya görüntüleme izniniz yok.</value>
    <comment>Error message for account not found</comment>
  </data>
  <data name="Savings Account Opening" xml:space="preserve">
    <value>Tasarruf Hesabı Açma</value>
    <comment>Trade type for RZW savings account opening</comment>
  </data>
  <data name="Savings Accounts" xml:space="preserve">
    <value>Tasarruf Hesapları</value>
    <comment>Admin menu item for savings accounts management</comment>
  </data>
  <data name="Savings Dashboard" xml:space="preserve">
    <value>Tasarruf Hesabı Paneli</value>
    <comment>Savings dashboard title</comment>
  </data>
  <data name="Savings History" xml:space="preserve">
    <value>Tasarruf Hesabı Geçmişi</value>
    <comment>Page title for savings account history</comment>
  </data>
  <data name="Savings Interest" xml:space="preserve">
    <value>Tasarruf Hesabı Faizi</value>
    <comment>Trade type for RZW savings interest payment</comment>
  </data>
  <data name="Savings Interest Details" xml:space="preserve">
    <value>Tasarruf Faizi Detayları</value>
    <comment>Title for savings interest details section</comment>
  </data>
  <data name="Savings Interest RZW" xml:space="preserve">
    <value>Tasarruf Faizi RZW</value>
    <comment>Column header for savings interest RZW amount</comment>
  </data>
  <data name="Savings Maturity" xml:space="preserve">
    <value>Tasarruf Hesabı Vadesi</value>
    <comment>Trade type for RZW savings maturity</comment>
  </data>
  <data name="Savings Plans" xml:space="preserve">
    <value>Tasarruf Hesabı Planları</value>
    <comment>Admin menu item for savings plans management</comment>
  </data>
  <data name="Scan to copy address" xml:space="preserve">
    <value>Adresi kopyalamak için tarayın</value>
    <comment>Label for QR code scanning</comment>
  </data>
  <data name="Search" xml:space="preserve">
    <value>Ara</value>
    <comment>Search label</comment>
  </data>
  <data name="Search by name, email, phone or referral code" xml:space="preserve">
    <value>İsim, e-posta, telefon veya referans kodu ile ara</value>
    <comment>Used in referral tree page</comment>
  </data>
  <data name="Search Users" xml:space="preserve">
    <value>Kullanıcı Ara</value>
    <comment>Used in referral tree page</comment>
  </data>
  <data name="Searching..." xml:space="preserve">
    <value>Aranıyor...</value>
    <comment>Text shown when searching in Select2 dropdown</comment>
  </data>
  <data name="seconds" xml:space="preserve">
    <value>saniye</value>
    <comment>Unit for seconds</comment>
  </data>
  <data name="Secure 256-bit TLS-encryption" xml:space="preserve">
    <value>Güvenli 256-bit TLS-şifreleme</value>
    <comment>Security information text</comment>
  </data>
  <data name="Secure Fast Innovative" xml:space="preserve">
    <value>Güvenli, hizlı, yenilikçi</value>
    <comment>Features for the second slider</comment>
  </data>
  <data name="Secure platform with insurance" xml:space="preserve">
    <value>Sigortalı güvenli platform</value>
    <comment>Benefit 2</comment>
  </data>
  <data name="Security Text" xml:space="preserve">
    <value>Hesap güvenliğinizden siz sorumlusunuz. Şifrenizi ve kimlik doğrulama bilgilerinizi güvende tutmanız gerekmektedir. Şüpheli bir aktivite fark ettiğinizde derhal müşteri hizmetlerimize bildirmelisiniz.</value>
    <comment>Text for security section in user agreement</comment>
  </data>
  <data name="Security Title" xml:space="preserve">
    <value>Güvenlik</value>
    <comment>Title for security section in user agreement</comment>
  </data>
  <data name="Select a bank account from the dropdown menu." xml:space="preserve">
    <value>Açılır menüden bir banka hesabı seçin.</value>
    <comment>Instruction for bank account selection</comment>
  </data>
  <data name="Select a pair from the API to automatically fill the Pair Code field" xml:space="preserve">
    <value>Çift Kodu alanını otomatik olarak doldurmak için API'den bir çift seçin</value>
    <comment>Instruction for API pair selection</comment>
  </data>
  <data name="Select a plan and enter amount to see interest preview" xml:space="preserve">
    <value>Faiz önizlemesini görmek için bir plan seçin ve miktar girin</value>
    <comment>Placeholder text for interest preview</comment>
  </data>
  <data name="Select a user to login as" xml:space="preserve">
    <value>Giriş yapmak için bir kullanıcı seçin</value>
    <comment>Instruction text for selecting a user</comment>
  </data>
  <data name="Select API Service" xml:space="preserve">
    <value>API Servisi Seçin</value>
    <comment>Label for API service selection</comment>
  </data>
  <data name="Select Bank" xml:space="preserve">
    <value>Banka Seçiniz</value>
    <comment>Bank selection field label</comment>
  </data>
  <data name="Select Bank Account" xml:space="preserve">
    <value>Banka Hesabı Seçin</value>
    <comment>Label for bank account selection</comment>
  </data>
  <data name="Select Coin" xml:space="preserve">
    <value>Coin Seçin</value>
    <comment>Label for coin selection</comment>
  </data>
  <data name="Select Language" xml:space="preserve">
    <value>Dil Seçin</value>
    <comment>Label for language selection</comment>
  </data>
  <data name="Select Package" xml:space="preserve">
    <value>Paket Seçin</value>
    <comment>Label for package selection dropdown</comment>
  </data>
  <data name="Select Pair" xml:space="preserve">
    <value>Çift Seçin</value>
    <comment>Label for pair selection</comment>
  </data>
  <data name="Select Savings Plan" xml:space="preserve">
    <value>Tasarruf Hesabı Planı Seçin</value>
    <comment>Section title for plan selection</comment>
  </data>
  <data name="Select User" xml:space="preserve">
    <value>Kullanıcı Seçin</value>
    <comment>Label for user selection</comment>
  </data>
  <data name="Selected bank not found." xml:space="preserve">
    <value>Seçilen banka bulunamadı.</value>
    <comment>Error message when selected bank is not found</comment>
  </data>
  <data name="Selected cryptocurrency is not available" xml:space="preserve">
    <value>Seçilen kripto para mevcut değil</value>
    <comment>Error message for unavailable cryptocurrency</comment>
  </data>
  <data name="Selected savings plan has invalid interest rate" xml:space="preserve">
    <value>Seçilen tasarruf hesabı planının faiz oranı geçersizdir</value>
    <comment>Error message for plans with invalid interest rates</comment>
  </data>
  <data name="Selected savings plan has invalid term duration" xml:space="preserve">
    <value>Seçilen tasarruf planının vade süresi geçersiz</value>
    <comment>Validation message for invalid plan term duration</comment>
  </data>
  <data name="Selected savings plan has invalid term type" xml:space="preserve">
    <value>Seçilen tasarruf planının vade türü geçersiz</value>
    <comment>Validation message for invalid plan term type</comment>
  </data>
  <data name="Selected User" xml:space="preserve">
    <value>Seçilen Kullanıcı</value>
    <comment>Used in referral tree page</comment>
  </data>
  <data name="Selected user does not have the User role" xml:space="preserve">
    <value>Seçilen kullanıcı Kullanıcı rolüne sahip değil</value>
    <comment>Error message when selected user does not have the User role</comment>
  </data>
  <data name="Selected user not found or is inactive" xml:space="preserve">
    <value>Seçilen kullanıcı bulunamadı veya aktif değil</value>
    <comment>Error message when selected user is not found or inactive</comment>
  </data>
  <data name="Sell" xml:space="preserve">
    <value>Sat</value>
    <comment>Sell button text</comment>
  </data>
  <data name="Sell Coin" xml:space="preserve">
    <value>Coin Sat</value>
    <comment>Label for sell coin button</comment>
  </data>
  <data name="Sell Price" xml:space="preserve">
    <value>Satış Fiyatı</value>
    <comment>Sell price column header</comment>
  </data>
  <data name="Send" xml:space="preserve">
    <value>Gönder</value>
    <comment>Send button</comment>
  </data>
  <data name="Send Message" xml:space="preserve">
    <value>Mesaj Gönder</value>
    <comment>Send message button</comment>
  </data>
  <data name="Send Verification Email" xml:space="preserve">
    <value>Doğrulama E-postası Gönder</value>
    <comment>Button to send verification email</comment>
  </data>
  <data name="Sender Address" xml:space="preserve">
    <value>Gönderen Adresi</value>
    <comment>Label for sender address field</comment>
  </data>
  <data name="Sender Full Name" xml:space="preserve">
    <value>Gönderici Ad Soyad</value>
    <comment>Sender full name label</comment>
  </data>
  <data name="Sender Full Name Required" xml:space="preserve">
    <value>Gönderici ad soyad gereklidir</value>
    <comment>Sender full name validation message</comment>
  </data>
  <data name="Sending" xml:space="preserve">
    <value>Gönderiliyor</value>
    <comment>Loading state text</comment>
  </data>
  <data name="Service Changes Text" xml:space="preserve">
    <value>RAZEWIN, hizmetlerinde ve bu sözleşmede değişiklik yapma hakkını saklı tutar. Önemli değişiklikler kullanıcılara bildirilecektir.</value>
    <comment>Text for service changes section in user agreement</comment>
  </data>
  <data name="Service Changes Title" xml:space="preserve">
    <value>Hizmet Değişiklikleri</value>
    <comment>Title for service changes section in user agreement</comment>
  </data>
  <data name="Service Description Text" xml:space="preserve">
    <value>RAZEWIN, kullanıcılarına kripto para alım-satım, saklama ve yatırım hizmetleri sunan bir dijital varlık platformudur.</value>
    <comment>Text for service description section in user agreement</comment>
  </data>
  <data name="Service Description Title" xml:space="preserve">
    <value>Hizmet Tanımı</value>
    <comment>Title for service description section in user agreement</comment>
  </data>
  <data name="Service Name" xml:space="preserve">
    <value>Servis Adı</value>
    <comment>Background service name column header</comment>
  </data>
  <data name="Settings" xml:space="preserve">
    <value>Ayarlar</value>
    <comment>Settings menu item</comment>
  </data>
  <data name="Share this code with friends to refer them to our platform." xml:space="preserve">
    <value>Bu kodu arkadaşlarınızla paylaşarak onları platformumuza davet edebilirsiniz.</value>
    <comment>Help text explaining how to use referral code</comment>
  </data>
  <data name="Share this link with friends to directly register them with your referral code." xml:space="preserve">
    <value>Bu linki arkadaşlarınızla paylaşarak doğrudan sizin referans kodunuzla kayıt olmalarını sağlayabilirsiniz.</value>
    <comment>Help text for the referral link field</comment>
  </data>
  <data name="Short Name" xml:space="preserve">
    <value>Kısa Ad</value>
    <comment>Label for short name field</comment>
  </data>
  <data name="Show Inactive Accounts" xml:space="preserve">
    <value>Pasif Hesapları Göster</value>
    <comment>Button text to show inactive savings accounts</comment>
  </data>
  <data name="Showing _START_ to _END_ of _TOTAL_ entries" xml:space="preserve">
    <value>_TOTAL_ kayıttan _START_ - _END_ arası gösteriliyor</value>
    <comment>Pagination info text</comment>
  </data>
  <data name="Sign In" xml:space="preserve">
    <value>Giriş Yap</value>
    <comment>Label for sign in button</comment>
  </data>
  <data name="Sign in to start your session" xml:space="preserve">
    <value>Oturumunuzu başlatmak için giriş yapın</value>
    <comment>Label for sign in page</comment>
  </data>
  <data name="Social Media" xml:space="preserve">
    <value>Sosyal Medya</value>
    <comment>Social media section title</comment>
  </data>
  <data name="Solutions" xml:space="preserve">
    <value>Çözümler</value>
    <comment>Solutions menu item</comment>
  </data>
  <data name="Staking" xml:space="preserve">
    <value>Staking</value>
    <comment>Staking title</comment>
  </data>
  <data name="Stand out with modern and impressive digital signage solutions" xml:space="preserve">
    <value>Modern ve etkileyici dijital tabela çözümleriyle öne çıkın</value>
    <comment>Digital signage tagline</comment>
  </data>
  <data name="Start Date" xml:space="preserve">
    <value>Başlangıç Tarihi</value>
    <comment>Savings account start date</comment>
  </data>
  <data name="Start earning interest on your RZW tokens with our savings plans." xml:space="preserve">
    <value>Vadeli hesap planlarımızla RZW tokenlarınızdan faiz kazanmaya başlayın.</value>
    <comment>Welcome message for new users</comment>
  </data>
  <data name="Start Investing" xml:space="preserve">
    <value>Yatırıma Başla!</value>
    <comment>Start investing step title</comment>
  </data>
  <data name="Start Investing in 3 Steps" xml:space="preserve">
    <value>3 Adımda Yatırıma Başlayın!</value>
    <comment>3 steps title</comment>
  </data>
  <data name="Start Investment Now" xml:space="preserve">
    <value>ŞİMDİ YATIRIMA BAŞLA</value>
    <comment>Button text for the second slider</comment>
  </data>
  <data name="Start Lending" xml:space="preserve">
    <value>Borç Vermeye Başla</value>
    <comment>Start lending button</comment>
  </data>
  <data name="Start Saving" xml:space="preserve">
    <value>Tasarrufa Başla</value>
    <comment>Start saving button</comment>
  </data>
  <data name="Start Staking" xml:space="preserve">
    <value>Staking'e Başla</value>
    <comment>Start staking button</comment>
  </data>
  <data name="Start Trading Now" xml:space="preserve">
    <value>Hemen İşlem Yap</value>
    <comment>Start trading button</comment>
  </data>
  <data name="Started" xml:space="preserve">
    <value>Başladı</value>
    <comment>Label for start date</comment>
  </data>
  <data name="Status" xml:space="preserve">
    <value>Durum</value>
    <comment>Status column header</comment>
  </data>
  <data name="Stay up-to-date with instant updates and programmable content changes." xml:space="preserve">
    <value>Anlık güncellemeler ve programlanabilir içerik değişiklikleriyle güncel kalın.</value>
    <comment>Dynamic content description</comment>
  </data>
  <data name="Subject" xml:space="preserve">
    <value>Konu</value>
    <comment>Subject field placeholder</comment>
  </data>
  <data name="Submit" xml:space="preserve">
    <value>Gönder</value>
    <comment>Submit button text</comment>
  </data>
  <data name="Submit Buy Order" xml:space="preserve">
    <value>Alış Emri Gönder</value>
    <comment>Buy order submit button text</comment>
  </data>
  <data name="Submit Deposit Report" xml:space="preserve">
    <value>Yatırım Raporu Gönder</value>
    <comment>Label for deposit report submission button</comment>
  </data>
  <data name="Submit Sell Order" xml:space="preserve">
    <value>Satış Emri Gönder</value>
    <comment>Sell order submit button text</comment>
  </data>
  <data name="Submit the form to notify us of your payment." xml:space="preserve">
    <value>Ödemenizi bildirmek için formu gönderin.</value>
    <comment>Instruction for payment notification</comment>
  </data>
  <data name="Submit the withdrawal request." xml:space="preserve">
    <value>Para çekme talebini gönderin.</value>
    <comment>Withdrawal step 3</comment>
  </data>
  <data name="Submitted" xml:space="preserve">
    <value>Gönderildi</value>
    <comment>Submitted status</comment>
  </data>
  <data name="Success" xml:space="preserve">
    <value>Başarılı</value>
    <comment>Success message title</comment>
  </data>
  <data name="Success Count" xml:space="preserve">
    <value>Başarı Sayısı</value>
    <comment>Number of successful executions of background service</comment>
  </data>
  <data name="Successfully saved" xml:space="preserve">
    <value>Başarıyla kaydedildi</value>
    <comment>Success message for save operation</comment>
  </data>
  <data name="Successfully updated" xml:space="preserve">
    <value>Başarıyla güncellendi</value>
    <comment>Success message for update operation</comment>
  </data>
  <data name="Summary" xml:space="preserve">
    <value>Özet</value>
  </data>
  <data name="Support" xml:space="preserve">
    <value>Destek</value>
    <comment>Support label</comment>
  </data>
  <data name="Surname" xml:space="preserve">
    <value>Soyad</value>
    <comment>Surname field label</comment>
  </data>
  <data name="System Monitoring" xml:space="preserve">
    <value>Sistem İzleme</value>
    <comment>Admin page title for system monitoring</comment>
  </data>
  <data name="Term Duration" xml:space="preserve">
    <value>Vade Süresi</value>
    <comment>Savings term duration</comment>
  </data>
  <data name="Term Type" xml:space="preserve">
    <value>Vade Türü</value>
    <comment>Savings account term type label</comment>
  </data>
  <data name="Terms and Conditions" xml:space="preserve">
    <value>Şartlar ve Koşullar</value>
    <comment>Label for terms and conditions</comment>
  </data>
  <data name="Text copied to clipboard" xml:space="preserve">
    <value>Metin panoya kopyalandı</value>
    <comment>Success message when text is copied to clipboard</comment>
  </data>
  <data name="Text copied to clipboard using fallback" xml:space="preserve">
    <value>Metin yedek yöntem kullanılarak panoya kopyalandı</value>
    <comment>Success message when text is copied to clipboard using fallback method</comment>
  </data>
  <data name="Text For Description" xml:space="preserve">
    <value>Açıklamaya yazılacak metin</value>
    <comment>Description text label</comment>
  </data>
  <data name="Thank you for registering with RazeWin. To complete your registration, please verify your email address by clicking the button below:" xml:space="preserve">
    <value>RazeWin'e kaydolduğunuz için teşekkür ederiz. Kaydınızı tamamlamak için aşağıdaki butona tıklayarak e-posta adresinizi doğrulayın:</value>
    <comment>Email verification instruction</comment>
  </data>
  <data name="The package reward percentages were set to zero for the relevant levels." xml:space="preserve">
    <value>İlgili seviyeler için paket ödül yüzdeleri sıfıra ayarlanmıştı.</value>
  </data>
  <data name="The reward will be added to each referrer's RZW wallet." xml:space="preserve">
    <value>Ödül, her referansın RZW cüzdanına eklenecektir.</value>
  </data>
  <data name="The system will find all users in the referral chain of the user who made this payment." xml:space="preserve">
    <value>Sistem, bu ödemeyi yapan kullanıcının referans zincirindeki tüm kullanıcıları bulacaktır.</value>
  </data>
  <data name="Theme Selector" xml:space="preserve">
    <value>Tema Seçici</value>
    <comment>Label for the theme selector UI</comment>
  </data>
  <data name="There are no users eligible for rewards in the referral chain for this payment." xml:space="preserve">
    <value>Bu ödeme için yönlendirme zincirinde ödül almaya uygun kullanıcı bulunmamaktadır.</value>
    <comment>Message explaining why there are no rewards to distribute</comment>
  </data>
  <data name="There were no users in the referral chain." xml:space="preserve">
    <value>Referans zincirinde hiç kullanıcı yoktu.</value>
  </data>
  <data name="This action is irreversible" xml:space="preserve">
    <value>Bu işlem geri alınamaz</value>
    <comment>Warning message for irreversible action</comment>
  </data>
  <data name="This amount will be deducted from your account balance." xml:space="preserve">
    <value>Bu tutar hesap bakiyenizden düşülecektir.</value>
    <comment>Purchase information message</comment>
  </data>
  <data name="This balance transaction record will be permanently deleted" xml:space="preserve">
    <value>Bu bakiye işlemi kaydı kalıcı olarak silinecektir</value>
    <comment>Warning message when deleting a balance transaction</comment>
  </data>
  <data name="This field is read-only once API is enabled" xml:space="preserve">
    <value>API etkinleştirildiğinde bu alan salt okunurdur</value>
    <comment>Note for API-enabled fields</comment>
  </data>
  <data name="This field is read-only when using API" xml:space="preserve">
    <value>API kullanırken bu alan salt okunurdur</value>
    <comment>Note for API fields</comment>
  </data>
  <data name="This is the unique identifier for your transaction on the blockchain." xml:space="preserve">
    <value>Bu, blok zincirindeki işleminiz için benzersiz tanımlayıcıdır.</value>
    <comment>Description for transaction hash</comment>
  </data>
  <data name="This is your available balance after pending withdrawals" xml:space="preserve">
    <value>Bu, bekleyen çekim işlemlerinden sonra kalan kullanılabilir bakiyenizdir</value>
    <comment>Explanation for available withdrawal limit</comment>
  </data>
  <data name="This payment method is under construction. Please try again later." xml:space="preserve">
    <value>Bu ödeme yöntemi yapım aşamasındadır. Lütfen daha sonra tekrar deneyiniz.</value>
    <comment>Under construction message</comment>
  </data>
  <data name="This payment record will be permanently deleted" xml:space="preserve">
    <value>Bu ödeme kaydı kalıcı olarak silinecektir</value>
    <comment>Warning for payment record deletion</comment>
  </data>
  <data name="This record will be permanently deleted" xml:space="preserve">
    <value>Bu kayıt kalıcı olarak silinecektir</value>
    <comment>Warning for record deletion</comment>
  </data>
  <data name="This referral code has reached its invite limit. Please use a different code." xml:space="preserve">
    <value>Bu referans kodu davet limitine ulaştı. Lütfen farklı bir kod kullanın.</value>
    <comment>Error message shown during registration when referrer has reached invite limit</comment>
  </data>
  <data name="This savings account has a zero interest rate. Please contact support if this is unexpected." xml:space="preserve">
    <value>Bu tasarruf hesabının faiz oranı sıfırdır. Bu beklenmedik bir durumsa lütfen destek ile iletişime geçin.</value>
    <comment>Warning message for zero interest rate accounts</comment>
  </data>
  <data name="This setting cannot be changed once enabled" xml:space="preserve">
    <value>Bu ayar etkinleştirildikten sonra değiştirilemez</value>
    <comment>Warning for irreversible settings</comment>
  </data>
  <data name="This setting record will be permanently deleted" xml:space="preserve">
    <value>Bu ayar kaydı kalıcı olarak silinecektir</value>
    <comment>Warning for setting record deletion</comment>
  </data>
  <data name="This trade record will be permanently deleted" xml:space="preserve">
    <value>Bu işlem kaydı kalıcı olarak silinecektir</value>
    <comment>Warning for trade record deletion</comment>
  </data>
  <data name="This tree shows the users referred by" xml:space="preserve">
    <value>Bu ağaç, şu kullanıcının getirdiği kullanıcıları gösterir:</value>
    <comment>Used in referral tree page</comment>
  </data>
  <data name="This user hasn't referred any users yet." xml:space="preserve">
    <value>Bu kullanıcı henüz kimseyi getirmemiş.</value>
    <comment>Used in referral tree page</comment>
  </data>
  <data name="This value will be added to the buy and sell prices. Can be negative." xml:space="preserve">
    <value>Bu değer alış ve satış fiyatlarına eklenecektir. Negatif olabilir.</value>
    <comment>Description for price adjustment field</comment>
  </data>
  <data name="This verification link will expire in 24 hours." xml:space="preserve">
    <value>Bu doğrulama bağlantısının süresi 24 saat içinde dolacaktır.</value>
    <comment>Token expiry warning</comment>
  </data>
  <data name="This wallet record will be permanently deleted" xml:space="preserve">
    <value>Bu cüzdan kaydı kalıcı olarak silinecektir</value>
    <comment>Warning for wallet record deletion</comment>
  </data>
  <data name="This withdrawal record will be permanently deleted" xml:space="preserve">
    <value>Bu para çekme kaydı kalıcı olarak silinecek</value>
    <comment>Warning message for withdrawal deletion</comment>
  </data>
  <data name="Time Since Last Success" xml:space="preserve">
    <value>Son Başarıdan Bu Yana Geçen Süre</value>
    <comment>Time elapsed since last successful run of background service</comment>
  </data>
  <data name="To Date" xml:space="preserve">
    <value>Bitiş Tarihi</value>
    <comment>To date filter label</comment>
  </data>
  <data name="To deposit funds into your account, please follow these steps:" xml:space="preserve">
    <value>Hesabınıza para yatırmak için lütfen şu adımları izleyin:</value>
    <comment>Instruction for deposit process</comment>
  </data>
  <data name="To withdraw funds from your account, please follow these steps:" xml:space="preserve">
    <value>Hesabınızdan para çekmek için lütfen şu adımları izleyin:</value>
    <comment>Withdrawal instructions intro</comment>
  </data>
  <data name="Tomorrow" xml:space="preserve">
    <value>Yarın</value>
    <comment>Label for tomorrow</comment>
  </data>
  <data name="Top {0} balances" xml:space="preserve">
    <value>En yüksek {0} bakiye</value>
    <comment>Parameterized message for showing top N balances</comment>
  </data>
  <data name="Total" xml:space="preserve">
    <value>Toplam</value>
    <comment>Total column header</comment>
  </data>
  <data name="Total Accounts" xml:space="preserve">
    <value>Toplam Hesap</value>
    <comment>Label for total accounts count</comment>
  </data>
  <data name="Total Amount Must Be Greater Than Zero" xml:space="preserve">
    <value>Toplam tutar sıfırdan büyük olmalıdır</value>
    <comment>Validation message for amount</comment>
  </data>
  <data name="Total Balance" xml:space="preserve">
    <value>Toplam Bakiye</value>
    <comment>Total balance label</comment>
  </data>
  <data name="Total Earned" xml:space="preserve">
    <value>Toplam Kazanılan</value>
    <comment>Label for total earned amount</comment>
  </data>
  <data name="Total Interest" xml:space="preserve">
    <value>Toplam Faiz</value>
    <comment>Total interest amount</comment>
  </data>
  <data name="Total Interest at Maturity" xml:space="preserve">
    <value>Vade Sonunda Toplam Faiz</value>
    <comment>Label for total interest at maturity</comment>
  </data>
  <data name="Total Interest Earned" xml:space="preserve">
    <value>Toplam Kazanılan Faiz</value>
    <comment>Total interest earned from savings</comment>
  </data>
  <data name="Total Investment" xml:space="preserve">
    <value>Toplam Yatırım</value>
    <comment>Total amount invested in savings</comment>
  </data>
  <data name="Total Locked RZW" xml:space="preserve">
    <value>Toplam Kilitli RZW</value>
    <comment>Label for total locked RZW amount</comment>
  </data>
  <data name="Total Payments" xml:space="preserve">
    <value>Toplam Ödeme</value>
    <comment>Total number of payments</comment>
  </data>
  <data name="total results" xml:space="preserve">
    <value>toplam sonuç</value>
    <comment>Total results label for pagination</comment>
  </data>
  <data name="Total Rewards" xml:space="preserve">
    <value>Toplam Ödüller</value>
    <comment>Label for total rewards count</comment>
  </data>
  <data name="Total RZW" xml:space="preserve">
    <value>Toplam RZW</value>
    <comment>Column header for total RZW amount</comment>
  </data>
  <data name="Total RZW Distributed" xml:space="preserve">
    <value>Toplam Dağıtılan RZW</value>
  </data>
  <data name="Total RZW to Distribute" xml:space="preserve">
    <value>Dağıtılacak Toplam RZW</value>
    <comment>Label for the total amount of RZW tokens to be distributed</comment>
  </data>
  <data name="Total TL to Distribute" xml:space="preserve">
    <value>Dağıtılacak Toplam TL</value>
    <comment>Label for the total amount of TL to be distributed</comment>
  </data>
  <data name="Total Value (TRY)" xml:space="preserve">
    <value>Toplam Değer (TRY)</value>
  </data>
  <data name="Trade" xml:space="preserve">
    <value>İşlem</value>
    <comment>Label for trade</comment>
  </data>
  <data name="Trade Activity Check" xml:space="preserve">
    <value>Ticaret Aktivitesi Kontrolü</value>
    <comment>Check item for trade activity</comment>
  </data>
  <data name="Trade Loss" xml:space="preserve">
    <value>İşlem Zararı</value>
    <comment>Transaction type for trade losses</comment>
  </data>
  <data name="Trade Profit" xml:space="preserve">
    <value>İşlem Kârı</value>
    <comment>Transaction type for trade profits</comment>
  </data>
  <data name="Trade successfully saved" xml:space="preserve">
    <value>İşlem başarıyla kaydedildi</value>
    <comment>Success message for trade saving</comment>
  </data>
  <data name="Trades" xml:space="preserve">
    <value>İşlemler</value>
    <comment>Trades menu item</comment>
  </data>
  <data name="Trading Fee" xml:space="preserve">
    <value>İşlem Ücreti</value>
    <comment>Trading fee label</comment>
  </data>
  <data name="Trading Volume" xml:space="preserve">
    <value>İşlem Hacmi</value>
    <comment>Trading volume label</comment>
  </data>
  <data name="Transaction Date:" xml:space="preserve">
    <value>İşlem Tarihi:</value>
    <comment>Transaction date label</comment>
  </data>
  <data name="Transaction Hash" xml:space="preserve">
    <value>İşlem Hash'i</value>
    <comment>Label for transaction hash field</comment>
  </data>
  <data name="Transaction Hash/ID" xml:space="preserve">
    <value>İşlem Hash/ID</value>
    <comment>Label for transaction hash/ID field</comment>
  </data>
  <data name="Transaction History" xml:space="preserve">
    <value>İşlem Geçmişi</value>
    <comment>Label for transaction history section</comment>
  </data>
  <data name="Transaction Reference" xml:space="preserve">
    <value>İşlem Referansı</value>
    <comment>Label for transaction reference field</comment>
  </data>
  <data name="Transaction Reversal" xml:space="preserve">
    <value>İşlem Geri Alma</value>
    <comment>Transaction reversal module title</comment>
  </data>
  <data name="Transaction Risks Text" xml:space="preserve">
    <value>Kripto para yatırımları yüksek risk içerir. Kaybetmeyi göze alamayacağınız miktarda yatırım yapmayınız. Geçmiş performans, gelecekteki sonuçların garantisi değildir.</value>
    <comment>Text for transaction risks section in user agreement</comment>
  </data>
  <data name="Transaction Risks Title" xml:space="preserve">
    <value>İşlem Riskleri</value>
    <comment>Title for transaction risks section in user agreement</comment>
  </data>
  <data name="Transaction Type" xml:space="preserve">
    <value>İşlem Türü</value>
    <comment>Label for transaction type field</comment>
  </data>
  <data name="Transfer the amount you wish to deposit to the selected bank account." xml:space="preserve">
    <value>Yatırmak istediğiniz tutarı seçilen banka hesabına transfer edin.</value>
    <comment>Instruction for bank transfer</comment>
  </data>
  <data name="Transfer Time" xml:space="preserve">
    <value>Transfer Saati</value>
    <comment>Transfer time field label</comment>
  </data>
  <data name="Transfer Time Required" xml:space="preserve">
    <value>Transfer saati gereklidir</value>
    <comment>Validation message for transfer time</comment>
  </data>
  <data name="Transfer Time:" xml:space="preserve">
    <value>Transfer Saati:</value>
    <comment>Transfer time label</comment>
  </data>
  <data name="Transfer Your Account" xml:space="preserve">
    <value>Hesabınızı Transfer Edin</value>
    <comment>Transfer account title</comment>
  </data>
  <data name="Try adjusting your filter criteria or check back later." xml:space="preserve">
    <value>Filtre kriterlerinizi ayarlamayı deneyin veya daha sonra tekrar kontrol edin.</value>
    <comment>Suggestion when no payments found</comment>
  </data>
  <data name="TRY Balance" xml:space="preserve">
    <value>TRY Bakiye</value>
    <comment>TRY Balance label</comment>
  </data>
  <data name="TRY Value (Approx.)" xml:space="preserve">
    <value>TRY Değeri (Yaklaşık)</value>
    <comment>Label for approximate TRY value</comment>
  </data>
  <data name="Type" xml:space="preserve">
    <value>Tip</value>
    <comment>Type label</comment>
  </data>
  <data name="Unable to load savings data. Please try again later." xml:space="preserve">
    <value>Vadeli hesap verileri yüklenemedi. Lütfen daha sonra tekrar deneyin.</value>
    <comment>Error message when savings data cannot be loaded</comment>
  </data>
  <data name="Under Construction" xml:space="preserve">
    <value>Yapım Aşamasında</value>
    <comment>Under construction title</comment>
  </data>
  <data name="Unique Accounts" xml:space="preserve">
    <value>Benzersiz Hesaplar</value>
    <comment>Label for unique accounts count</comment>
  </data>
  <data name="Unique Users" xml:space="preserve">
    <value>Benzersiz Kullanıcılar</value>
    <comment>Label for unique users count</comment>
  </data>
  <data name="Unknown" xml:space="preserve">
    <value>Bilinmiyor</value>
    <comment>Default text for unknown values</comment>
  </data>
  <data name="<EMAIL>" xml:space="preserve">
    <value><EMAIL></value>
    <comment>Default email for unknown users</comment>
  </data>
  <data name="Unlimited" xml:space="preserve">
    <value>Sınırsız</value>
    <comment>Label for unlimited</comment>
  </data>
  <data name="Upgrade" xml:space="preserve">
    <value>Yükselt</value>
    <comment>Button text for upgrading to a higher package</comment>
  </data>
  <data name="Upgrade Package" xml:space="preserve">
    <value>Paketi Yükselt</value>
    <comment>Button text to upgrade package</comment>
  </data>
  <data name="upgrade to" xml:space="preserve">
    <value>yükseltmek istediğinize</value>
    <comment>Text used in upgrade confirmation message</comment>
  </data>
  <data name="USDT (TRC20) Deposit Address" xml:space="preserve">
    <value>USDT (TRC20) Yatırma Adresi</value>
    <comment>Label for USDT deposit address</comment>
  </data>
  <data name="USDT Address" xml:space="preserve">
    <value>USDT Adresi</value>
    <comment>Label for USDT address field</comment>
  </data>
  <data name="User" xml:space="preserve">
    <value>Kullanıcı</value>
    <comment>User label</comment>
  </data>
  <data name="User Agreement" xml:space="preserve">
    <value>Kullanıcı Sözleşmesi</value>
    <comment>User Agreement page title</comment>
  </data>
  <data name="User Agreement Title" xml:space="preserve">
    <value>RAZEWIN Kullanıcı Sözleşmesi</value>
    <comment>Title for the main user agreement</comment>
  </data>
  <data name="User already exists" xml:space="preserve">
    <value>Kullanıcı zaten mevcut</value>
    <comment>Error message for existing user</comment>
  </data>
  <data name="User Balance" xml:space="preserve">
    <value>Kullanıcı Bakiyesi</value>
    <comment>User balance label</comment>
  </data>
  <data name="User does not have enough balance" xml:space="preserve">
    <value>Kullanıcının yeterli bakiyesi yok</value>
    <comment>Error message when user doesn't have enough balance</comment>
  </data>
  <data name="User does not have enough coin balance" xml:space="preserve">
    <value>Kullanıcının yeterli coin bakiyesi yok</value>
    <comment>Error message when user doesn't have enough cryptocurrency balance</comment>
  </data>
  <data name="User does not have enough TRY balance" xml:space="preserve">
    <value>Kullanıcının yeterli TRY bakiyesi yok</value>
    <comment>Error message when user doesn't have enough TRY balance</comment>
  </data>
  <data name="User has made {0} trades after this deposit was approved" xml:space="preserve">
    <value>Kullanıcı bu para yatırma işlemi onaylandıktan sonra {0} ticaret yapmış</value>
    <comment>Status message for trade activity check with count</comment>
  </data>
  <data name="User has made {0} withdrawals after this deposit was approved" xml:space="preserve">
    <value>Kullanıcı bu para yatırma işlemi onaylandıktan sonra {0} para çekme işlemi yapmış</value>
    <comment>Status message for withdrawal activity check with count</comment>
  </data>
  <data name="User has purchased {0} packages after this deposit was approved" xml:space="preserve">
    <value>Kullanıcı bu para yatırma işlemi onaylandıktan sonra {0} paket satın almış</value>
    <comment>Status message for package purchase check with count</comment>
  </data>
  <data name="User has sufficient balance for reversal" xml:space="preserve">
    <value>Kullanıcının geri alma için yeterli bakiyesi var</value>
    <comment>Status message for balance sufficiency check</comment>
  </data>
  <data name="User List" xml:space="preserve">
    <value>Kullanıcı Listesi</value>
    <comment>Label for user list section</comment>
  </data>
  <data name="User Name:" xml:space="preserve">
    <value>Kullanıcı Adı:</value>
    <comment>User name label</comment>
  </data>
  <data name="User not found" xml:space="preserve">
    <value>Kullanıcı bulunamadı</value>
    <comment>Error message for user not found</comment>
  </data>
  <data name="User not found." xml:space="preserve">
    <value>Kullanıcı bulunamadı.</value>
    <comment>User not found error short</comment>
  </data>
  <data name="User not found. Please log in again." xml:space="preserve">
    <value>Kullanıcı bulunamadı. Lütfen tekrar giriş yapın.</value>
    <comment>User not found error</comment>
  </data>
  <data name="User Package" xml:space="preserve">
    <value>Kullanıcı Paketi</value>
    <comment>Label for user package</comment>
  </data>
  <data name="User Packages" xml:space="preserve">
    <value>Kullanıcı Paketleri</value>
    <comment>Navigation menu item for user packages</comment>
  </data>
  <data name="User Statistics" xml:space="preserve">
    <value>Kullanıcı İstatistikleri</value>
    <comment>Page title for user statistics report</comment>
  </data>
  <data name="Users" xml:space="preserve">
    <value>Kullanıcılar</value>
    <comment>Label for users section</comment>
  </data>
  <data name="User's current balance ({0}) is less than deposit amount ({1})" xml:space="preserve">
    <value>Kullanıcının mevcut bakiyesi ({0}) para yatırma tutarından ({1}) az</value>
    <comment>Status message for balance sufficiency check with amounts</comment>
  </data>
  <data name="USERS EXCEEDED" xml:space="preserve">
    <value>KULLANICIYI GEÇTİ</value>
    <comment>Users count text</comment>
  </data>
  <data name="Users in the referral chain did not have active packages." xml:space="preserve">
    <value>Referans zincirindeki kullanıcıların aktif paketleri yoktu.</value>
  </data>
  <data name="Users Registered with Your Referral Code" xml:space="preserve">
    <value>Referans Kodunuzla Kayıt Olan Kullanıcılar</value>
    <comment>More descriptive label for referred users list</comment>
  </data>
  <data name="Users to be Rewarded" xml:space="preserve">
    <value>Ödüllendirilecek Kullanıcılar</value>
    <comment>Label for the number of users who will receive rewards</comment>
  </data>
  <data name="Value" xml:space="preserve">
    <value>Değer</value>
    <comment>Label for value field</comment>
  </data>
  <data name="Value (TRY)" xml:space="preserve">
    <value>Değer (TRY)</value>
  </data>
  <data name="Verification Checks" xml:space="preserve">
    <value>Doğrulama Kontrolleri</value>
    <comment>Title for verification checks section</comment>
  </data>
  <data name="Verification email sent successfully. Please check your inbox." xml:space="preserve">
    <value>Doğrulama e-postası başarıyla gönderildi. Lütfen gelen kutunuzu kontrol edin.</value>
    <comment>Success message for email sent</comment>
  </data>
  <data name="Verified" xml:space="preserve">
    <value>Doğrulandı</value>
    <comment>Verification status - verified</comment>
  </data>
  <data name="Verify Email Address" xml:space="preserve">
    <value>E-posta Adresini Doğrula</value>
    <comment>Email verification button text</comment>
  </data>
  <data name="View All" xml:space="preserve">
    <value>Tümünü Görüntüle</value>
    <comment>Link text to view all items</comment>
  </data>
  <data name="View All Accounts" xml:space="preserve">
    <value>Tüm Hesapları Görüntüle</value>
    <comment>Link text to view all savings accounts</comment>
  </data>
  <data name="View All Announcements" xml:space="preserve">
    <value>Tüm Duyuruları Görüntüle</value>
    <comment>Link to view all announcements</comment>
  </data>
  <data name="View All Interest History" xml:space="preserve">
    <value>Tüm Faiz Geçmişini Görüntüle</value>
    <comment>Button to view all interest history</comment>
  </data>
  <data name="VIEW ALL LIVE STREAM" xml:space="preserve">
    <value>TÜM CANLI AKIŞI GÖRÜNTÜLE</value>
    <comment>View all live stream button text</comment>
  </data>
  <data name="View Available Packages" xml:space="preserve">
    <value>Mevcut Paketleri Görüntüle</value>
    <comment>Button text to view packages</comment>
  </data>
  <data name="View Details" xml:space="preserve">
    <value>Detayları Görüntüle</value>
    <comment>Button text to view detailed information</comment>
  </data>
  <data name="View Packages" xml:space="preserve">
    <value>Paketleri Görüntüle</value>
    <comment>Button text to view packages</comment>
  </data>
  <data name="View Referrer" xml:space="preserve">
    <value>Referans Edeni Görüntüle</value>
    <comment>Used in referral tree page</comment>
  </data>
  <data name="View Reward Summary" xml:space="preserve">
    <value>Ödül Özetini Görüntüle</value>
    <comment>Button text for viewing reward summary</comment>
  </data>
  <data name="View Savings Details" xml:space="preserve">
    <value>Tasarruf Hesabı Detayları</value>
    <comment>Button to view savings details</comment>
  </data>
  <data name="Vision" xml:space="preserve">
    <value>Vizyon</value>
    <comment>Vision title</comment>
  </data>
  <data name="Wallets" xml:space="preserve">
    <value>Cüzdanlar</value>
    <comment>Label for wallets section</comment>
  </data>
  <data name="Warning" xml:space="preserve">
    <value>Uyarı</value>
    <comment>Warning message title</comment>
  </data>
  <data name="Warning: Deleting this package may affect referral rewards" xml:space="preserve">
    <value>Uyarı: Bu paketi silmek referans ödüllerini etkileyebilir</value>
    <comment>Warning message for deleting a package</comment>
  </data>
  <data name="Warning: Editing a balance transaction will not update user balances. Use with caution." xml:space="preserve">
    <value>Uyarı: Bakiye işlemini düzenlemek kullanıcı bakiyelerini güncellemeyecektir. Dikkatli kullanın.</value>
    <comment>Warning message when editing a balance transaction</comment>
  </data>
  <data name="Warning: Editing a trade will not update user balances. Use with caution." xml:space="preserve">
    <value>Uyarı: Bir ticareti düzenlemek kullanıcı bakiyelerini güncellemeyecektir. Dikkatli kullanın.</value>
    <comment>Warning message when editing trades</comment>
  </data>
  <data name="Week" xml:space="preserve">
    <value>Hafta</value>
    <comment>Column header for week identifier</comment>
  </data>
  <data name="Weekly Distribution Summary" xml:space="preserve">
    <value>Haftalık Dağıtım Özeti</value>
    <comment>Title for weekly distribution summary table</comment>
  </data>
  <data name="Welcome back! Here are your active savings accounts." xml:space="preserve">
    <value>Tekrar hoş geldiniz! İşte aktif tasarruf hesaplarınız.</value>
    <comment>Welcome message for users with active savings</comment>
  </data>
  <data name="Welcome to RazeWin" xml:space="preserve">
    <value>RazeWin'e Hoş Geldiniz</value>
    <comment>Welcome panel title</comment>
  </data>
  <data name="What Does RazeWin Do" xml:space="preserve">
    <value>RazeWin Ne Yapar</value>
    <comment>RazeWin services description page title</comment>
  </data>
  <data name="What is Coin" xml:space="preserve">
    <value>Coin Nedir</value>
    <comment>What is Coin page title</comment>
  </data>
  <data name="What Is It" xml:space="preserve">
    <value>Nedir</value>
    <comment>What Is It page title</comment>
  </data>
  <data name="What Our Customers Say" xml:space="preserve">
    <value>Müşterilerimiz Ne Diyor?</value>
    <comment>Testimonials section title</comment>
  </data>
  <data name="WhatsApp" xml:space="preserve">
    <value>WhatsApp</value>
    <comment>Label for WhatsApp field</comment>
  </data>
  <data name="When a user purchases a package, they will receive RZW tokens equal to the package price." xml:space="preserve">
    <value>When a user purchases a package, they will receive RZW tokens equal to the package price.</value>
    <comment>Information about RZW tokens given with package purchase</comment>
  </data>
  <data name="When you distribute rewards for this payment:" xml:space="preserve">
    <value>Bu ödeme için ödülleri dağıttığınızda:</value>
  </data>
  <data name="When you purchase a new package, any previous packages will be deactivated" xml:space="preserve">
    <value>Yeni bir paket satın aldığınızda, önceki paketleriniz devre dışı bırakılacaktır</value>
    <comment>Message informing users about package deactivation</comment>
  </data>
  <data name="Who Referred Whom" xml:space="preserve">
    <value>Kim Kimi Getirdi</value>
    <comment>Used in referral hierarchy view</comment>
  </data>
  <data name="Why Digital Signage?" xml:space="preserve">
    <value>Neden Dijital Tabela?</value>
    <comment>Why digital signage section title</comment>
  </data>
  <data name="Why Earn with RazeWin?" xml:space="preserve">
    <value>Neden RazeWin ile Kazanmalısınız?</value>
    <comment>Why earn section title</comment>
  </data>
  <data name="WILL BE ANNOUNCED ON JUNE 25TH" xml:space="preserve">
    <value>25 HAZİRANDA AÇIKLANIYOR!</value>
    <comment>Full announcement text for RZW Token slider</comment>
  </data>
  <data name="Withdraw Early" xml:space="preserve">
    <value>Erken Çek</value>
    <comment>Early withdrawal button text</comment>
  </data>
  <data name="Withdraw Funds" xml:space="preserve">
    <value>Para Çek</value>
    <comment>Withdraw funds button/title</comment>
  </data>
  <data name="Withdrawal" xml:space="preserve">
    <value>Para Çekme</value>
    <comment>Withdrawal page title</comment>
  </data>
  <data name="Withdrawal Activity Check" xml:space="preserve">
    <value>Para Çekme Aktivitesi Kontrolü</value>
    <comment>Check item for withdrawal activity</comment>
  </data>
  <data name="Withdrawal Amount" xml:space="preserve">
    <value>Çekim Tutarı</value>
    <comment>Withdrawal amount label</comment>
  </data>
  <data name="Withdrawal History" xml:space="preserve">
    <value>Para Çekme Geçmişi</value>
    <comment>Withdrawal history title</comment>
  </data>
  <data name="Withdrawal Information" xml:space="preserve">
    <value>Para Çekme Bilgileri</value>
    <comment>Label for withdrawal information section</comment>
  </data>
  <data name="Withdrawal Instructions" xml:space="preserve">
    <value>Para Çekme Talimatları</value>
    <comment>Withdrawal instructions title</comment>
  </data>
  <data name="Withdrawal Request Submitted" xml:space="preserve">
    <value>Para çekme talebi başarıyla gönderildi</value>
    <comment>Success message for withdrawal submission</comment>
  </data>
  <data name="Withdrawal request submitted successfully" xml:space="preserve">
    <value>Para çekme talebiniz başarıyla gönderildi. Talebiniz 1-3 iş günü içinde işleme alınacaktır.</value>
    <comment>Success message for withdrawal submission</comment>
  </data>
  <data name="Withdrawal reversal: {0}" xml:space="preserve">
    <value>Para Çekme İptali: {0}</value>
    <comment>Description for withdrawal reversal transaction</comment>
  </data>
  <data name="Withdrawal Submission Error" xml:space="preserve">
    <value>Para çekme talebi gönderilirken hata oluştu: {0}</value>
    <comment>Error message for withdrawal submission</comment>
  </data>
  <data name="Withdrawal: {0}" xml:space="preserve">
    <value>Para Çekme: {0}</value>
    <comment>Description for withdrawal transaction</comment>
  </data>
  <data name="Withdrawals" xml:space="preserve">
    <value>Para Çekimleri</value>
    <comment>Withdrawals menu item</comment>
  </data>
  <data name="Withdrawn" xml:space="preserve">
    <value>Çekilmiş</value>
    <comment>Status for withdrawn savings accounts</comment>
  </data>
  <data name="Write Exact Text In Papara Description" xml:space="preserve">
    <value>Papara açıklama kısmına yukarıdaki metni aynen yazınız. İşleminiz daha hızlı onaylanacaktır.</value>
    <comment>Papara description instruction</comment>
  </data>
  <data name="Year" xml:space="preserve">
    <value>Yıl</value>
    <comment>Singular form of year</comment>
  </data>
  <data name="Yearly" xml:space="preserve">
    <value>Yıllık</value>
    <comment>Yearly period label</comment>
  </data>
  <data name="Yearly Distribution Summary" xml:space="preserve">
    <value>Yıllık Dağıtım Özeti</value>
    <comment>Title for yearly distribution summary table</comment>
  </data>
  <data name="Years" xml:space="preserve">
    <value>Yıl</value>
    <comment>Years label</comment>
  </data>
  <data name="Years of Experience" xml:space="preserve">
    <value>Yıllık Deneyim</value>
    <comment>Years of experience label</comment>
  </data>
  <data name="Yes" xml:space="preserve">
    <value>Evet</value>
    <comment>Yes text</comment>
  </data>
  <data name="You can invite" xml:space="preserve">
    <value>Davet edebilirsiniz</value>
    <comment>Part of message showing remaining invites</comment>
  </data>
  <data name="You can send another verification email in {0} minutes." xml:space="preserve">
    <value>{0} dakika sonra başka bir doğrulama e-postası gönderebilirsiniz.</value>
    <comment>Rate limiting message with time</comment>
  </data>
  <data name="You can upgrade to a higher package at any time." xml:space="preserve">
    <value>İstediğiniz zaman daha yüksek bir pakete yükseltebilirsiniz.</value>
    <comment>Information about upgrading packages</comment>
  </data>
  <data name="You can withdraw your RZW tokens early, but you may receive reduced interest based on the holding period." xml:space="preserve">
    <value>RZW tokenlarınızı erken çekebilirsiniz, ancak tutma süresine göre azaltılmış faiz alabilirsiniz.</value>
    <comment>Early withdrawal warning text</comment>
  </data>
  <data name="You cannot purchase a lower package than your current one" xml:space="preserve">
    <value>Mevcut paketinizden daha düşük bir paket satın alamazsınız</value>
    <comment>Error message when trying to purchase a lower package</comment>
  </data>
  <data name="You do not have permission to access the admin area" xml:space="preserve">
    <value>Yönetici alanına erişim izniniz yok</value>
    <comment>Error message for admin area access</comment>
  </data>
  <data name="You do not have permission to view this page" xml:space="preserve">
    <value>Bu sayfayı görüntüleme izniniz yok</value>
    <comment>Error message for page access</comment>
  </data>
  <data name="You don't have an active package" xml:space="preserve">
    <value>Aktif bir paketiniz bulunmamaktadır</value>
    <comment>Message when user has no package</comment>
  </data>
  <data name="You don't have enough balance to purchase this package." xml:space="preserve">
    <value>Bu paketi satın almak için yeterli bakiyeniz bulunmamaktadır.</value>
    <comment>Error message for insufficient balance</comment>
  </data>
  <data name="You don't have permission to claim interest for this account or it doesn't exist." xml:space="preserve">
    <value>Bu hesap için faiz almaya yetkiniz yok veya hesap bulunamadı.</value>
    <comment>Error message for unauthorized claim attempt.</comment>
  </data>
  <data name="You have reached the daily limit ({0}/{1}). Try again tomorrow." xml:space="preserve">
    <value>Günlük limite ulaştınız ({0}/{1}). Yarın tekrar deneyin.</value>
    <comment>Daily limit with count message</comment>
  </data>
  <data name="You have reached the daily limit for verification emails. Please try again tomorrow." xml:space="preserve">
    <value>Doğrulama e-postaları için günlük limite ulaştınız. Lütfen yarın tekrar deneyin.</value>
    <comment>Daily limit reached message</comment>
  </data>
  <data name="You have reached your invite limit. Upgrade your package to invite more users." xml:space="preserve">
    <value>Davet limitinize ulaştınız. Daha fazla kullanıcı davet etmek için paketinizi yükseltin.</value>
    <comment>Message shown when user has reached their invite limit</comment>
  </data>
  <data name="You haven't referred any users yet. Share your referral code to start referring friends!" xml:space="preserve">
    <value>Henüz referans olduğunuz kullanıcı bulunmamaktadır. Arkadaşlarınızı davet etmek için referans kodunuzu paylaşın!</value>
    <comment>Message shown when user has no referred users</comment>
  </data>
  <data name="You need to purchase a package to start inviting users." xml:space="preserve">
    <value>Kullanıcıları davet etmeye başlamak için bir paket satın almanız gerekiyor.</value>
    <comment>Message shown when user has no package</comment>
  </data>
  <data name="Your account allows unlimited invites." xml:space="preserve">
    <value>Hesabınız sınırsız davet yapmanıza olanak tanır.</value>
    <comment>Message shown when user has no package but can invite unlimited users</comment>
  </data>
  <data name="Your account will be credited once the payment is verified." xml:space="preserve">
    <value>Ödeme doğrulandıktan sonra hesabınıza para yatırılacaktır.</value>
    <comment>Message for payment verification</comment>
  </data>
  <data name="Your Balance" xml:space="preserve">
    <value>Bakiyeniz</value>
    <comment>Label for user balance display</comment>
  </data>
  <data name="Your cryptocurrency deposit request has been received. It will be processed as soon as possible." xml:space="preserve">
    <value>Kripto para yatırma talebiniz alınmıştır. En kısa sürede işleme alınacaktır.</value>
    <comment>Confirmation message for cryptocurrency deposit</comment>
  </data>
  <data name="Your current balance" xml:space="preserve">
    <value>Mevcut bakiyeniz</value>
    <comment>Label for current balance</comment>
  </data>
  <data name="Your Current Package" xml:space="preserve">
    <value>Mevcut Paketiniz</value>
    <comment>Section heading for current package</comment>
  </data>
  <data name="Your current package allows unlimited invites." xml:space="preserve">
    <value>Mevcut paketiniz sınırsız davet sağlar.</value>
    <comment>Message shown when user has unlimited invites</comment>
  </data>
  <data name="Your deposit request has been received. It will be processed as soon as possible." xml:space="preserve">
    <value>Para yatırma talebiniz alındı. En kısa sürede işleme alınacaktır.</value>
    <comment>Success message for deposit submission</comment>
  </data>
  <data name="Your Email" xml:space="preserve">
    <value>E-posta Adresiniz</value>
    <comment>Email field placeholder</comment>
  </data>
  <data name="Your email is already verified." xml:space="preserve">
    <value>E-posta adresiniz zaten doğrulanmış.</value>
    <comment>Already verified message</comment>
  </data>
  <data name="Your Gateway to the Digital World" xml:space="preserve">
    <value>Dijital Dünyaya Açılan Kapınız</value>
    <comment>Main tagline</comment>
  </data>
  <data name="Your Message" xml:space="preserve">
    <value>Mesajınız</value>
    <comment>Message field placeholder</comment>
  </data>
  <data name="Your Name" xml:space="preserve">
    <value>Adınız</value>
    <comment>Name field placeholder</comment>
  </data>
  <data name="Your request will be processed within 1-3 business days." xml:space="preserve">
    <value>Talebiniz 1-3 iş günü içinde işleme alınacaktır.</value>
    <comment>Withdrawal step 4</comment>
  </data>
  <data name="Your Rights Text" xml:space="preserve">
    <value>Kişisel verilerinize erişim, düzeltme, silme ve işleme itiraz etme hakkına sahipsiniz. Bu haklarınızı kullanmak için müşteri hizmetlerimizle iletişime geçebilirsiniz.</value>
    <comment>Text for your rights section in privacy agreement</comment>
  </data>
  <data name="Your Rights Title" xml:space="preserve">
    <value>Haklarınız</value>
    <comment>Title for your rights section in privacy agreement</comment>
  </data>
  <data name="Your RZW tokens will be locked until maturity date" xml:space="preserve">
    <value>RZW tokenlarınız vade tarihine kadar kilitlenecek</value>
    <comment>Warning text about token locking</comment>
  </data>
  <data name="Your trusted partner for cryptocurrency investments. Start trading today and experience the difference." xml:space="preserve">
    <value>Kripto para yatırımları için güvenilir ortağınız. Bugün işlem yapmaya başlayın ve farkı deneyimleyin.</value>
    <comment>Welcome panel description</comment>
  </data>
  <data name="Email Not Verified" xml:space="preserve">
    <value>E-posta Doğrulanmadı</value>
    <comment>Email verification status - not verified</comment>
  </data>
  <data name="An error occurred while sending the verification email." xml:space="preserve">
    <value>Doğrulama e-postası gönderilirken bir hata oluştu.</value>
    <comment>Error message when email verification fails</comment>
  </data>
  <data name="Email Verified Successfully" xml:space="preserve">
    <value>E-posta Başarıyla Doğrulandı</value>
    <comment>Success title for email verification</comment>
  </data>
  <data name="Your email address has been verified successfully. You can now access all features of your account." xml:space="preserve">
    <value>E-posta adresiniz başarıyla doğrulandı. Artık hesabınızın tüm özelliklerine erişebilirsiniz.</value>
    <comment>Success message for email verification completion</comment>
  </data>
</root>